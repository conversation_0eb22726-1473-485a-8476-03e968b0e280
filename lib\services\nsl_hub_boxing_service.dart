import 'package:dio/dio.dart';
import '../models/nsl_sentence_model.dart';

class NslHubBoxingService {
  static const String _baseUrl = 'http://10.26.1.52:8084/api/nslhubboxing';
  late final Dio _dio;

  NslHubBoxingService() {
    _dio = Dio();
    _dio.options.headers['Content-Type'] = 'application/json';
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  /// Fetches NSL hub boxing data from the API
  Future<NslSentences?> fetchNslHubBoxingData(String goId) async {
    try {
      final response = await _dio.get(
        _baseUrl,
        queryParameters: {
          'goId': goId,
        },
      );

      if (response.statusCode == 200) {
        return NslSentences.fromJson(response.data);
      } else {
        print('Failed to load NSL hub boxing data: ${response.statusCode}');
        print('Response data: ${response.data}');
        return null;
      }
    } on DioException catch (e) {
      print('Dio error fetching NSL hub boxing data: ${e.message}');
      print('Error type: ${e.type}');
      if (e.response != null) {
        print('Response status: ${e.response?.statusCode}');
        print('Response data: ${e.response?.data}');
      }
      return null;
    } catch (e) {
      print('Error fetching NSL hub boxing data: $e');
      return null;
    }
  }

  /// Groups the data by postgres_groupname while preserving API order
  Map<String, List<Datum>> groupDataByPostgresGroupname(List<Datum> data) {
    // Use LinkedHashMap to preserve insertion order
    Map<String, List<Datum>> groupedData = <String, List<Datum>>{};
    
    for (var item in data) {
      String groupName = item.postgresGroupname ?? 'Unknown';
      
      if (!groupedData.containsKey(groupName)) {
        groupedData[groupName] = [];
      }
      
      groupedData[groupName]!.add(item);
    }
    
    return groupedData;
  }

  /// Gets display text for an item, prioritizing natural_language over postgres_name
  String getDisplayText(Datum item) {
    if (item.naturalLanguage != null && item.naturalLanguage!.isNotEmpty) {
      return item.naturalLanguage!;
    } else if (item.postgresName != null && item.postgresName!.isNotEmpty) {
      return item.postgresName!;
    } else {
      return 'Unknown';
    }
  }

  /// Sorts groups alphabetically and items within groups by display text
  Map<String, List<Datum>> sortGroupedData(Map<String, List<Datum>> groupedData) {
    Map<String, List<Datum>> sortedData = {};
    
    // Sort group names alphabetically
    var sortedKeys = groupedData.keys.toList()..sort();
    
    for (String key in sortedKeys) {
      // Sort items within each group by display text
      List<Datum> sortedItems = groupedData[key]!;
      sortedItems.sort((a, b) => getDisplayText(a).compareTo(getDisplayText(b)));
      sortedData[key] = sortedItems;
    }
    
    return sortedData;
  }

  /// Fetches and processes NSL hub boxing data
  Future<Map<String, List<Datum>>?> fetchAndProcessData(String goId) async {
    try {
      final nslSentences = await fetchNslHubBoxingData(goId);
      
      if (nslSentences?.data != null) {
        final groupedData = groupDataByPostgresGroupname(nslSentences!.data!);
        // Return grouped data without sorting to preserve API order
        return groupedData;
      }
      
      return null;
    } catch (e) {
      print('Error processing NSL hub boxing data: $e');
      return null;
    }
  }
}
