import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:camera/camera.dart'; // Add this dependency

import 'package:camera/camera.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart'; // Add this dependency

class CaptureImageWidget extends StatefulWidget {
  final double size;
  final bool isCircular;
  final bool useFrontCamera;
  final double imageQuality;
  final bool allowGallery;
  final String? resolution;
  final bool autoCapture;
  final String? buttonText;
  final bool showPreview;
  final String? displayName;
  final bool showDisplayName;
  final ValueChanged<File>? onImageSelected;
  final bool? testAutoOpen;
  final Uint8List? testImage;

  const CaptureImageWidget({
    super.key,
    this.size = 120,
    this.isCircular = true,
    this.useFrontCamera = false,
    this.imageQuality = 0.8,
    this.allowGallery = true,
    this.resolution,
    this.autoCapture = false,
    this.buttonText,
    this.showPreview = true,
    this.displayName,
    this.showDisplayName = true,
    this.onImageSelected,
    this.testAutoOpen,
    this.testImage,
  });

  factory CaptureImageWidget.fromJson(Map<String, dynamic> json) {
    return CaptureImageWidget(
      size: (json['size'] as num?)?.toDouble() ?? 120.0,
      isCircular: json['isCircular'] ?? true,
      useFrontCamera: json['useFrontCamera'] ?? false,
      imageQuality: (json['imageQuality'] as num?)?.toDouble() ?? 0.8,
      allowGallery: json['allowGallery'] ?? true,
      resolution: json['resolution'] as String?,
      autoCapture: json['autoCapture'] ?? false,
      buttonText: json['buttonText'] as String?,
      showPreview: json['showPreview'] ?? true,
      displayName: json['displayName'] as String?,
      showDisplayName: json['showDisplayName'] ?? true,
      onImageSelected: (file) {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'size': size,
      'isCircular': isCircular,
      'useFrontCamera': useFrontCamera,
      'imageQuality': imageQuality,
      'allowGallery': allowGallery,
      if (resolution != null) 'resolution': resolution,
      'autoCapture': autoCapture,
      if (buttonText != null) 'buttonText': buttonText,
      'showPreview': showPreview,
      if (displayName != null) 'displayName': displayName,
      'showDisplayName': showDisplayName,
    };
  }

  @override
  State<CaptureImageWidget> createState() => _CaptureImageWidgetState();
}

class _CaptureImageWidgetState extends State<CaptureImageWidget>
    with TickerProviderStateMixin {
  File? _imageFile;
  Uint8List? _webImage;
  bool _isHovering = false;
  bool _isImageHovering = false;
  bool _isActiveCamera = false;
  bool _isSaved = false;

  // Camera related variables
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  bool _isCameraInitialized = false;
  bool _isCameraPreviewActive = false;
  String? _cameraError;

  late AnimationController _hoverAnimationController;
  late Animation<double> _scaleAnimation;

  bool get _hasImage => kIsWeb ? _webImage != null : _imageFile != null;

  Widget _buildImageWidget() {
    if (kIsWeb) {
      if (_webImage != null) {
        return Image.memory(_webImage!, fit: BoxFit.cover);
      }
    } else {
      if (_imageFile != null) {
        return Image.file(_imageFile!, fit: BoxFit.cover);
      }
    }

    return Icon(
      widget.useFrontCamera ? Icons.face : Icons.camera_alt,
      size: widget.size / 3,
      color: Colors.black54,
    );
  }

  @override
  void initState() {
    super.initState();
    _initializeCamera();

    _hoverAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _hoverAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    if (widget.testImage != null) {
      setState(() {
        _webImage = widget.testImage;
      });
    }

    if (widget.autoCapture || widget.testAutoOpen == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.testAutoOpen != true) {
          _startCameraPreview();
        }
      });
    }
  }

  Future<void> _initializeCamera() async {
    try {
      // Check if cameras are available
      _cameras = await availableCameras();
      print('Available cameras: ${_cameras?.length}');
      for (var camera in _cameras ?? []) {
        print('Camera: ${camera.name}, Direction: ${camera.lensDirection}');
      }
    } catch (e) {
      print('Error getting available cameras: $e');
      setState(() {
        _cameraError = 'Failed to get cameras: $e';
      });
    }
  }

  Future<void> _startCameraPreview() async {
    if (_cameras == null || _cameras!.isEmpty) {
      await _initializeCamera(); // Try to initialize again
      if (_cameras == null || _cameras!.isEmpty) {
        setState(() {
          _cameraError = 'No cameras available on this device';
        });
        _showPermissionDeniedMessage();
        return;
      }
    }

    try {
      // Dispose any existing controller first
      await _disposeCameraController();

      // Select camera based on preference
      CameraDescription selectedCamera = _cameras!.first;
      if (widget.useFrontCamera) {
        selectedCamera = _cameras!.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.front,
          orElse: () => _cameras!.first,
        );
      } else {
        selectedCamera = _cameras!.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.back,
          orElse: () => _cameras!.first,
        );
      }

      print('Selected camera: ${selectedCamera.name}');

      // Create new controller
      _cameraController = CameraController(
        selectedCamera,
        ResolutionPreset.medium, // Try medium first, high can cause issues
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // Initialize the controller
      await _cameraController!.initialize();

      // Check if widget is still mounted after async operation
      if (!mounted) {
        await _disposeCameraController();
        return;
      }

      // Update UI state
      setState(() {
        _isCameraInitialized = true;
        _isCameraPreviewActive = true;
        _isActiveCamera = true;
        _cameraError = null;
      });

      print('Camera initialized successfully');
    } catch (e) {
      print('Error starting camera: $e');
      setState(() {
        _cameraError = 'Camera error: $e';
        _isCameraInitialized = false;
        _isCameraPreviewActive = false;
        _isActiveCamera = false;
      });

      // Clean up on error
      await _disposeCameraController();
      _showPermissionDeniedMessage();
    }
  }

  Future<void> _captureImage() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      print('Camera controller not initialized');
      return;
    }

    try {
      // Show loading state
      setState(() {
        _cameraError = null;
      });

      final XFile image = await _cameraController!.takePicture();
      print('Image captured: ${image.path}');

      if (kIsWeb) {
        final bytes = await image.readAsBytes();
        setState(() {
          _webImage = bytes;
          _imageFile = null;
          _isCameraPreviewActive = false;
          _isActiveCamera = false;
          _isSaved = true;
        });
      } else {
        setState(() {
          _imageFile = File(image.path);
          _webImage = null;
          _isCameraPreviewActive = false;
          _isActiveCamera = false;
          _isSaved = true;
        });
        widget.onImageSelected?.call(_imageFile!);
      }

      await _disposeCameraController();
    } catch (e) {
      print('Error capturing image: $e');
      setState(() {
        _cameraError = 'Failed to capture image: $e';
      });
    }
  }

  Future<void> _disposeCameraController() async {
    if (_cameraController != null) {
      try {
        await _cameraController!.dispose();
      } catch (e) {
        print('Error disposing camera controller: $e');
      }
      _cameraController = null;
    }

    if (mounted) {
      setState(() {
        _isCameraInitialized = false;
        _isCameraPreviewActive = false;
      });
    }
  }

  @override
  void dispose() {
    _hoverAnimationController.dispose();
    // Use async dispose in a fire-and-forget manner
    _disposeCameraController();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      if (source == ImageSource.camera) {
        await _startCameraPreview();
      } else {
        await _openGallery();
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  Future<void> _openGallery() async {
    try {
      final ImagePicker picker = ImagePicker();

      int? maxWidth;
      int? maxHeight;

      if (widget.resolution != null) {
        final resolutionParts = widget.resolution!.split('x');
        if (resolutionParts.length == 2) {
          maxWidth = int.tryParse(resolutionParts[0]);
          maxHeight = int.tryParse(resolutionParts[1]);
        }
      }

      final int quality = (widget.imageQuality * 100).round();

      final XFile? picked = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
        imageQuality: quality,
      );

      if (picked != null) {
        if (kIsWeb) {
          final bytes = await picked.readAsBytes();
          setState(() {
            _webImage = bytes;
            _imageFile = null;
            _isActiveCamera = false;
            _isSaved = true;
          });
        } else {
          setState(() {
            _imageFile = File(picked.path);
            _webImage = null;
            _isActiveCamera = false;
            _isSaved = true;
          });
          widget.onImageSelected?.call(_imageFile!);
        }
      }
    } catch (e) {
      _showPermissionDeniedMessage();
    }
  }

  void _showPermissionDeniedMessage() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Camera permission denied. Please enable camera access in your device settings.',
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontSize: _getResponsiveFontSize(context),
            fontWeight: FontManager.medium,
          ),
        ),
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _deleteImage() {
    setState(() {
      _imageFile = null;
      _webImage = null;
      _isActiveCamera = false;
      _isSaved = false;
      _isImageHovering = false;
    });
  }

  Future<void> _closeCamera() async {
    await _disposeCameraController();
    setState(() {
      _isActiveCamera = false;
      _isCameraPreviewActive = false;
      _cameraError = null;
    });
  }

  double _getResponsiveContainerHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 244.0;
    } else if (screenWidth >= 1440) {
      return 240.5;
    } else if (screenWidth >= 1280) {
      return 232.0;
    } else if (screenWidth >= 768) {
      return 224.0;
    } else {
      return 224.0;
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0;
    } else if (screenWidth >= 1440) {
      return 14.0;
    } else if (screenWidth >= 1280) {
      return 12.0;
    } else {
      return 12.0;
    }
  }

  double _getResponsiveBoxsize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 8.0;
    } else if (screenWidth >= 1440) {
      return 8.0;
    } else if (screenWidth >= 1280) {
      return 6.0;
    } else if (screenWidth >= 768) {
      return 4.0;
    } else {
      return 4.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    // State 1: Default - Small camera icon
    if (!_hasImage && !_isActiveCamera && !_isCameraPreviewActive) {
      return _buildDefaultState();
    }

    // State 2: Camera Preview - Live camera with controls
    if (_isCameraPreviewActive && _isCameraInitialized) {
      return _buildCameraPreviewState();
    }

    // State 3: After Capture - Clean image preview only
    if (_isSaved && _hasImage) {
      return _buildSavedState();
    }

    // Loading state while camera initializes
    if (_isActiveCamera && !_isCameraInitialized) {
      return _buildLoadingState();
    }

    return _buildDefaultState();
  }

  Widget _buildDefaultState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showDisplayName)
          Text(
            widget.displayName ?? 'Capture Image',
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontSize: _getResponsiveFontSize(context),
              fontWeight: FontManager.medium,
            ),
          ),
        SizedBox(height: _getResponsiveBoxsize(context)),
        MouseRegion(
          onEnter: (_) {
            setState(() => _isHovering = true);
            _hoverAnimationController.forward();
          },
          onExit: (_) {
            setState(() => _isHovering = false);
            _hoverAnimationController.reverse();
          },
          child: GestureDetector(
            onTap: () => _pickImage(ImageSource.camera),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              transform: Matrix4.identity()..scale(_isHovering ? 1.1 : 1.0),
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 200),
                opacity: _isHovering ? 1 : 1.0,
                curve: Curves.easeInOut,
                child: SvgPicture.asset(
                  'assets/images/capture-image.svg',
                  key: ValueKey(_isHovering),
                  package: 'ui_controls_library',
                  fit: BoxFit.contain,
                  width: _getResponsiveAvatarWidth(context),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showDisplayName)
          Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Text(
              widget.displayName ?? 'Capture Image',
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontManager.medium,
              ),
            ),
          ),
        Container(
          width: 294,
          height: _getResponsiveContainerHeight(context),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 8),
                Text(
                  _cameraError ?? 'Initializing camera...',

                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: _getResponsiveFontSize(context),
                    fontWeight: FontManager.medium,
                    color: _cameraError != null ? Colors.red : Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                if (_cameraError != null) ...[
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _closeCamera,
                    child: Text(
                      'Close',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: _getResponsiveFontSize(context),
                        fontWeight: FontManager.medium,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCameraPreviewState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showDisplayName)
          Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Text(
              widget.displayName ?? 'Capture Image',
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontManager.medium,
              ),
            ),
          ),
        Container(
          width: 294,
          height: _getResponsiveContainerHeight(context),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Stack(
            children: [
              // Camera preview
              Positioned(
                top: 16,
                left: 16,
                right: 16,
                bottom: 48,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    width: 262,
                    height: 148,
                    child:
                        _cameraController != null &&
                                _cameraController!.value.isInitialized &&
                                !_cameraController!.value.hasError
                            ? AspectRatio(
                              aspectRatio: _cameraController!.value.aspectRatio,
                              child: CameraPreview(_cameraController!),
                            )
                            : Container(
                              color: Colors.black,
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    if (_cameraError != null) ...[
                                      Icon(
                                        Icons.error_outline,
                                        color: Colors.white,
                                        size: 32,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Camera Error',
                                        style: FontManager.getCustomStyle(
                                          fontFamily:
                                              FontManager.fontFamilyInter,
                                          fontSize: _getResponsiveFontSize(
                                            context,
                                          ),
                                          fontWeight: FontManager.medium,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                        ),
                                        child: Text(
                                          _cameraError!,
                                          style: FontManager.getCustomStyle(
                                            fontFamily:
                                                FontManager.fontFamilyInter,
                                            fontSize: _getResponsiveFontSize(
                                              context,
                                            ),
                                            fontWeight: FontManager.medium,
                                            color: Colors.white70,
                                          ),
                                          textAlign: TextAlign.center,
                                          maxLines: 3,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ] else ...[
                                      const CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Loading camera...',
                                        style: FontManager.getCustomStyle(
                                          fontFamily:
                                              FontManager.fontFamilyInter,
                                          fontSize: _getResponsiveFontSize(
                                            context,
                                          ),
                                          fontWeight: FontManager.medium,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                  ),
                ),
              ),
              // Close button (X)
              Positioned(
                bottom: 8,
                right: 8,
                child: GestureDetector(
                  onTap: _closeCamera,
                  child: Container(
                    width: 24,
                    height: 24,
                    child: const Icon(
                      Icons.close,
                      color: Colors.black,
                      size: 16,
                    ),
                  ),
                ),
              ),
              // OK/Capture button
              Positioned(
                bottom: 8,
                right: 40, // Position next to close button
                child: GestureDetector(
                  onTap: _captureImage,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      color: Color(0xFF0058FF),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
              // Camera switch button (optional)
              Positioned(
                bottom: 8,
                left: 8,
                child: GestureDetector(
                  onTap: () => _pickImage(ImageSource.camera), // Restart camera
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      color: Color(0xFF0058FF),
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.asset(
                      'assets/images/capture-image.svg',
                      package: 'ui_controls_library',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
              // Camera switch button (optional)
              // Positioned(
              //   bottom: 8,
              //   left: 8,
              //   child: GestureDetector(
              //     onTap: () => _pickImage(ImageSource.camera), // Restart camera
              //     child: Container(
              //       width: 24,
              //       height: 24,
              //       decoration: const BoxDecoration(
              //         color: Color(0xFF0058FF),
              //         shape: BoxShape.circle,
              //       ),
              //       child: SvgPicture.asset(
              //         'assets/images/capture-image.svg',
              //         package: 'ui_controls_library',
              //         fit: BoxFit.contain,
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSavedState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showDisplayName)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.displayName ?? 'Capture Image',
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontManager.medium,
              ),
            ),
          ),
        MouseRegion(
          onEnter: (_) => setState(() => _isImageHovering = true),
          onExit: (_) => setState(() => _isImageHovering = false),
          child: Container(
            width: 294,
            height: 166,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(child: _buildImageWidget()),
                ),
                if (_isImageHovering)
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: _deleteImage,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(150),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.delete,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

double _getResponsiveAvatarWidth(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 48.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 40.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}
