// Widget that wraps the entire message content with hover functionality

import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/build_question_formatted_text_spans.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/file_upload_response_preview.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/message_action_buttons.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/quick_message_button.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/selectable_type_writer_text_widget.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/solution_parsed_render_widget.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_home_screen_chat.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:provider/provider.dart';

class TypingMarkdownBody extends StatefulWidget {
  final String data;
  final Duration typingSpeed;
  final VoidCallback? onComplete;
  final VoidCallback? onNewLine;
  final bool shouldAnimate;

  const TypingMarkdownBody({
    super.key,
    required this.data,
    this.typingSpeed = const Duration(milliseconds: 50),
    this.onComplete,
    this.onNewLine,
    this.shouldAnimate = true,
  });

  @override
  State<TypingMarkdownBody> createState() => _TypingMarkdownBodyState();
}

class _TypingMarkdownBodyState extends State<TypingMarkdownBody> {
  String _displayedText = '';
  Timer? _timer;
  int _currentIndex = 0;
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    if (widget.shouldAnimate && widget.data.isNotEmpty) {
      _startTyping();
    } else {
      _displayedText = widget.data;
      _isComplete = true;
    }
  }

  @override
  void didUpdateWidget(TypingMarkdownBody oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      _resetAnimation();
      if (widget.shouldAnimate && widget.data.isNotEmpty) {
        _startTyping();
      } else {
        _displayedText = widget.data;
        _isComplete = true;
      }
    }
  }

  void _resetAnimation() {
    _timer?.cancel();
    _currentIndex = 0;
    _displayedText = '';
    _isComplete = false;
  }

  void _startTyping() {
    _timer = Timer.periodic(widget.typingSpeed, (timer) {
      if (_currentIndex < widget.data.length) {
        setState(() {
          _displayedText = widget.data.substring(0, _currentIndex + 1);
          _currentIndex++;
        });

        // Check for new lines to trigger callback
        if (widget.onNewLine != null &&
            _currentIndex > 0 &&
            widget.data[_currentIndex - 1] == '\n') {
          widget.onNewLine!();
        }
      } else {
        timer.cancel();
        _isComplete = true;
        widget.onComplete?.call();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MarkdownBody(data: _displayedText);
  }
}

class MessageContentWithHover extends StatefulWidget {
  final ChatMessage message;
  final int index;
  final AudioPlayer audioPlayer;
  final String? currentPlayingMessageId;
  final bool isPlaying;
  final bool isPaused;
  final Duration currentPosition;
  final Duration totalDuration;
  final Function(String, {String? messageId}) onTextToSpeech;
  final VoidCallback onStopAudio;
  final Function(BuildContext) showCopyOverlay;
  final bool isLastItem;
  final dynamic parentState;
  final VoidCallback? onComplete;
  final VoidCallback? onNewLine;
  final VoidCallback? brdTap;
  final bool suggestionsAsCheckbox;

  const MessageContentWithHover({
    super.key,
    required this.message,
    required this.index,
    required this.audioPlayer,
    required this.currentPlayingMessageId,
    required this.isPlaying,
    required this.isPaused,
    required this.currentPosition,
    required this.totalDuration,
    required this.onTextToSpeech,
    required this.onStopAudio,
    required this.showCopyOverlay,
    required this.isLastItem,
    required this.parentState,
    this.onNewLine,
    this.onComplete,
    this.brdTap,
    this.suggestionsAsCheckbox = false,
  });

  @override
  State<MessageContentWithHover> createState() =>
      _MessageContentWithHoverState();
}

class _MessageContentWithHoverState extends State<MessageContentWithHover> {
  bool isHovered = false;
  List<String> _selectedSuggestions = [];

  // final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    // Always show buttons if audio is playing for this message
    final String messageId = 'msg_${widget.index}';
    final bool alwaysShow =
        widget.currentPlayingMessageId == messageId && widget.isPlaying ||
            (widget.isLastItem);
    final baseTextStyle = TextStyle(
      height: MediaQuery.of(context).size.width > 1600 ? 1.5 : 2,
      fontFamily: 'TiemposText',
      fontSize: MediaQuery.of(context).size.width > 1600 ? 17 : 15,
      fontWeight: FontWeight.w400,
      color: Colors.black,
    );

    final spans =
        buildQuestionFormattedTextSpans(widget.message.content, baseTextStyle);
    Widget textWidget = Container();
    if (widget.message.content.isNotEmpty &&
        widget.isLastItem &&
        !widget.message.isTypingComplete) {
      textWidget = SelectableTypewriterText(
        textSpans: spans,
        speed: Duration(
          milliseconds: widget.message.content.length > 500
              ? (widget.message.content.length / 200).toInt()
              : (widget.message.content.length / 50).toInt(),
        ),
        onNewline: () => widget.onNewLine?.call(),
        onComplete: () {
          if (!(widget.message.isTypingComplete)) {
            setState(() => widget.message.isTypingComplete = true);
            widget.onComplete?.call();
          }
        },
      );
    } else {
      textWidget = SelectableText.rich(TextSpan(children: spans));
    }
    if (widget.message.customContentItem == 1) {
      textWidget = Container();
    }

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display file preview if this is a response to a file upload
          if (!widget.message.isUser && widget.message.fileData != null)
            FileUploadResponsePreview(
              fileName:
                  widget.message.fileData!.data?.fileName ?? 'Uploaded File',
              onTap: () {
                if (widget.parentState.runtimeType == WebHomeScreenChatState) {
                  widget.parentState.ocrText =
                      widget.message.fileData!.data?.originalText ?? '';
                  widget.parentState.ocrFileName =
                      widget.message.fileData!.data?.fileName ?? '';
                  widget.parentState.fileUploadOcrResponse =
                      widget.message.fileData;
                  widget.parentState.toggleOcrPanel();
                }
              },
            ),

          // textWidget,
          if (widget.message.customContentItem != 1)
            TypingMarkdownBody(
              data: widget.message.content,
              shouldAnimate:
                  widget.isLastItem && !widget.message.isTypingComplete,
              typingSpeed: Duration(
                milliseconds: widget.message.content.length > 500
                    ? (widget.message.content.length / 200).toInt()
                    : (widget.message.content.length / 50).toInt(),
              ),
              onNewLine: () => widget.onNewLine?.call(),
              onComplete: () {
                if (!(widget.message.isTypingComplete)) {
                  setState(() => widget.message.isTypingComplete = true);
                  widget.onComplete?.call();
                }
              },
            ),
          if (widget.message.customContentItem == 1)
            ParsedResponseRenderer(
              responseText: widget.message.content,
              isTypingRequired:
                  widget.isLastItem && !widget.message.isTypingComplete,
              options: widget.message.customData1?.options ?? [],
              displayText: widget.message.customData1?.displayText ?? '',
              leadingQuestion:
                  widget.message.customData1?.leadingQuestion ?? '',
              trailingQuestion:
                  widget.message.customData1?.trailingQuestion ?? '',
              onOptionChanged: widget.message.extraFunction,
              onTypingComplete: () {
                if (!(widget.message.isTypingComplete)) {
                  setState(() => widget.message.isTypingComplete = true);
                  widget.onComplete?.call();
                }
              },
              isLast: widget.isLastItem,
              onNewLine: () => widget.onNewLine?.call(),
              typingSpeed: Duration(
                milliseconds: widget.message.content.length > 500
                    ? (widget.message.content.length / 200).toInt()
                    : (widget.message.content.length / 50).toInt(),
              ),
            ),
          const SizedBox(
            height: AppSpacing.md,
          ),
          if ((widget.message.showBRD != null && widget.message.showBRD!) &&
              ((widget.message.isTypingComplete)
              // && !widget.isLastItem
              ))
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: widget.brdTap ??
                    () {
                      final provider = Provider.of<WebHomeProviderStatic>(
                          context,
                          listen: false);
                      if (provider.showStatusArtifacts &&
                          widget.message.brdData == provider.lastBrdData) {
                        provider.toggleStatusArtifacts(null);
                      } else {
                        provider.toggleStatusArtifacts(widget.message.brdData);
                      }
                    },
                child: Container(
                  decoration: BoxDecoration(
                      color: Provider.of<WebHomeProviderStatic>(context, listen: false)
                                  .showStatusArtifacts &&
                              widget.message.brdData ==
                                  Provider.of<WebHomeProviderStatic>(context, listen: false)
                                      .lastBrdData
                          ? AppColors.grey
                          : AppColors.white,
                      borderRadius: BorderRadius.circular(AppSpacing.xs),
                      border: Border.all(
                          color: Provider.of<WebHomeProviderStatic>(context, listen: false)
                                      .showStatusArtifacts &&
                                  widget.message.brdData ==
                                      Provider.of<WebHomeProviderStatic>(
                                              context,
                                              listen: false)
                                          .lastBrdData
                              ? AppColors.primaryBlue
                              : AppColors.darkGreyBorder,
                          width: 0.5)),
                  padding: EdgeInsets.symmetric(
                      vertical: AppSpacing.sm, horizontal: AppSpacing.lg),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text("Business Requirement Document",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: FontManager.getCustomStyle(
                                fontFamily: FontManager.fontFamilyTiemposText,
                                fontWeight: widget.message.brdData ==
                                        Provider.of<WebHomeProviderStatic>(
                                                context,
                                                listen: false)
                                            .lastBrdData
                                    ? FontWeight.w600
                                    : FontWeight.w400)),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text("Progress: ${widget.message.brdExtraData}",
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: FontManager.getCustomStyle(
                                  fontSize: FontManager.s10,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  fontWeight: widget.message.brdData ==
                                          Provider.of<WebHomeProviderStatic>(
                                                  context,
                                                  listen: false)
                                              .lastBrdData
                                      ? FontWeight.w600
                                      : FontWeight.w400)),
                          const SizedBox(
                            width: AppSpacing.xxs,
                          ),
                          SvgPicture.asset(
                            "assets/images/artifact.svg",
                            height: 50,
                            width: 50,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          const SizedBox(
            height: AppSpacing.sm,
          ),
          // Follow-up suggestions - only show after typing is complete
          if (widget.message.followUpSuggestions != null &&
              widget.message.followUpSuggestions!.isNotEmpty &&
              _shouldShowFollowUps())
            Container(
              margin: EdgeInsets.only(top: AppSpacing.xs),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Follow-up suggestions:',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey.shade700,
                      fontFamily: 'TiemposText',
                    ),
                  ),
                  SizedBox(height: AppSpacing.xxs),
                  Wrap(
                    spacing: AppSpacing.xxs,
                    runSpacing: AppSpacing.xxs,
                    children: widget.message.followUpSuggestions!
                        .map((suggestion) =>
                            _buildFollowUpSuggestion(suggestion))
                        .toList(),
                  ),
                ],
              ),
            ),

          // Add spacing if both content and customContent are present
          if (widget.message.content.isNotEmpty &&
              widget.message.customContent != null)
            SizedBox(height: 16),

          // Display custom content if available
          if (widget.message.customContent != null)
            widget.message.customContent!,

          SizedBox(height: 8),

          // Message action buttons with hover functionality
          AnimatedOpacity(
            opacity: isHovered || alwaysShow ? 1.0 : 0.0,
            duration: Duration(milliseconds: 200),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                MessageActionButtons(
                  // Use content for TTS and copy operations
                  // If content is empty but we have customContent, use a default message
                  messageContent: widget.message.content.isNotEmpty
                      ? widget.message.content
                      : "I've processed your request. Please see the information above.",
                  messageId: messageId,
                  audioPlayer: widget.audioPlayer,
                  currentPlayingMessageId: widget.currentPlayingMessageId,
                  isPlaying: widget.isPlaying,
                  isPaused: widget.isPaused,
                  currentPosition: widget.currentPosition,
                  totalDuration: widget.totalDuration,
                  onTextToSpeech: widget.onTextToSpeech,
                  onStopAudio: widget.onStopAudio,
                  showCopyOverlay: widget.showCopyOverlay,
                ),
              ],
            ),
          ),
          SizedBox(height: AppSpacing.xs)
        ],
      ),
    );
  }

  // Helper method to determine when follow-up suggestions should be shown
  bool _shouldShowFollowUps() {
    // For messages with reasoning data, show follow-ups after NSL thinking completes
    // if (widget.message.reasoningData != null &&
    //     widget.message.reasoningData!.isNotEmpty) {
    //   return widget.message.hasNSLTypingCompleted ?? false;
    // }

    // For regular messages, show follow-ups after typing completes
    // If it's not the last item (no typing animation), show immediately
    // if (!widget.isLastItem) {
    //   return true;
    // }

    // For the last item, check if typing is complete
    return widget.isLastItem && widget.message.isTypingComplete;
  }

  // Build a follow-up suggestion button
  Widget _buildFollowUpSuggestion(String suggestion) {
    if (widget.suggestionsAsCheckbox) {
      final isSelected = _selectedSuggestions.contains(suggestion);

      return Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade50 : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Checkbox(
              value: isSelected,
              onChanged: (_) {
                setState(() {
                  if (isSelected) {
                    _selectedSuggestions.remove(suggestion);
                    if (widget.parentState != null &&
                        widget.parentState.chatController.text
                            .contains(suggestion)) {
                      widget.parentState.chatController.text = widget
                          .parentState.chatController.text
                          .replaceAll("$suggestion ", '');
                    }
                  } else {
                    _selectedSuggestions.add(suggestion);
                    if (widget.parentState != null &&
                        !widget.parentState.chatController.text
                            .contains(suggestion)) {
                      widget.parentState.chatController.text += "$suggestion ";
                    }
                  }
                });
              },
              visualDensity: VisualDensity.compact,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            Flexible(
              child: Text(
                suggestion,
                style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    color: isSelected ? Colors.blue : Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ],
        ),
      );
    } else {
      return QuickMessageButton(
        text: suggestion,
        imagePath: '',
        isSelected: false,
        onTap: () {
          if (widget.parentState != null &&
              !widget.parentState.chatController.text.contains(suggestion)) {
            widget.parentState.chatController.text = suggestion;
          }
          setState(() {});
        },
      );
    }

    // InkWell(
    //   onTap: () {
    //     // Handle suggestion tap - send the suggestion as a new message
    //     if (widget.parentState != null) {
    //       // Set the suggestion text in the chat controller
    //       if (!widget.parentState.chatController.text.contains(suggestion)) {
    //         widget.parentState.chatController.text = suggestion;
    //       }
    //       // Trigger send message
    //       // widget.parentState._sendMessage();
    //     }
    //   },
    //   borderRadius: BorderRadius.circular(16),
    //   child: Container(
    //     padding: EdgeInsets.symmetric(
    //       horizontal: AppSpacing.xs,
    //       vertical: AppSpacing.xxs,
    //     ),
    //     decoration: BoxDecoration(
    //       color: Colors.grey.shade100,
    //       borderRadius: BorderRadius.circular(16),
    //       border: Border.all(
    //         color: Colors.grey.shade300,
    //         width: 1,
    //       ),
    //     ),
    //     child: Text(
    //       suggestion,
    //       style: TextStyle(
    //         fontSize: 12,
    //         fontWeight: FontWeight.w400,
    //         color: Colors.grey.shade700,
    //         fontFamily: 'TiemposText',
    //       ),
    //     ),
    //   ),
    // );
  }
}
