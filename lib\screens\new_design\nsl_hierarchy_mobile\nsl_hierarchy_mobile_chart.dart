import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_mobile_node_card.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_mobile_node_detail_screen.dart';

class NSLHierarchyMobileChart extends StatefulWidget {
  final NSLNode rootNode;
  final Set<String> expandedNodes;
  final Function(String) onNodeInfoTap;
  final Function(NSLHierarchyData1) onNodeCircleTap;
  final String? selectedNodeId;
  
  const NSLHierarchyMobileChart({
    super.key,
    required this.rootNode,
    required this.expandedNodes,
    required this.onNodeInfoTap,
    required this.onNodeCircleTap,
    this.selectedNodeId,
  });
  
  @override
  State<NSLHierarchyMobileChart> createState() =>
      _NSLHierarchyMobileChartState();
}

class _NSLHierarchyMobile<PERSON><PERSON>State extends State<NSLHierarchyMobileChart>
    with TickerProviderStateMixin {
  final Map<String, double> _measuredChildrenHeights = {};
  Set<String> _previousExpandedNodes = {};
  String? _localSelectedNodeId;
  
  // Animation controllers for each node
  final Map<String, AnimationController> _animationControllers = {};
  final Map<String, Animation<double>> _animations = {};
  
  @override
  void initState() {
    super.initState();
    _previousExpandedNodes = Set.from(widget.expandedNodes);
    _localSelectedNodeId = widget.selectedNodeId;
    _initializeAnimations();
  }
  
  @override
  void dispose() {
    // Dispose all animation controllers
    for (final controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
  
 // Key optimizations for faster animations:


void _initializeAnimations() {
  _visitAllNodes(widget.rootNode, (node) {
    if (!_animationControllers.containsKey(node.id)) {
      final controller = AnimationController(
        duration: const Duration(milliseconds: 800), // Increased from 200ms
        vsync: this,
      );
      
      final animation = CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      );
      
      _animationControllers[node.id] = controller;
      _animations[node.id] = animation;
      
      if (widget.expandedNodes.contains(node.id)) {
        controller.value = 1.0;
      }
    }
  });
}
  
  void _visitAllNodes(NSLNode node, void Function(NSLNode) callback) {
    callback(node);
    for (final child in node.children) {
      _visitAllNodes(child, callback);
    }
  }
  
  @override
  void didUpdateWidget(NSLHierarchyMobileChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update local selected node ID if it changed from parent
    if (widget.selectedNodeId != oldWidget.selectedNodeId) {
      _localSelectedNodeId = widget.selectedNodeId;
    }
    
    // Check if expanded nodes have changed
    if (!_setsEqual(_previousExpandedNodes, widget.expandedNodes)) {
      _handleExpandedNodesChange();
      _clearHighlightedPathIfInvalid();
      _cleanupCollapsedNodeHeights();
      _previousExpandedNodes = Set.from(widget.expandedNodes);
    }
  }
  
  void _handleExpandedNodesChange() {
    // Find newly expanded nodes
    final newlyExpanded = widget.expandedNodes.difference(_previousExpandedNodes);
    // Find newly collapsed nodes
    final newlyCollapsed = _previousExpandedNodes.difference(widget.expandedNodes);
    
    // Initialize animations for any new nodes
    _initializeAnimations();
    
    // Animate newly expanded nodes
    for (final nodeId in newlyExpanded) {
      final controller = _animationControllers[nodeId];
      if (controller != null) {
        controller.forward();
      }
    }
    
    // Animate newly collapsed nodes
    for (final nodeId in newlyCollapsed) {
      final controller = _animationControllers[nodeId];
      if (controller != null) {
        controller.reverse();
      }
    }
  }
  
  bool _setsEqual(Set<String> set1, Set<String> set2) {
    if (set1.length != set2.length) return false;
    return set1.every((element) => set2.contains(element));
  }
  
  void _clearHighlightedPathIfInvalid() {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return;
    
    final path = _getPathToNode(widget.rootNode, selectedId);
    if (path.isEmpty) {
      setState(() {
        _localSelectedNodeId = null;
      });
      return;
    }
    
    for (int i = 0; i < path.length; i++) {
      final nodeId = path[i];
      final node = _findNodeById(widget.rootNode, nodeId);
      if (node != null && node.children.isNotEmpty) {
        if (!widget.expandedNodes.contains(nodeId)) {
          setState(() {
            _localSelectedNodeId = null;
          });
          return;
        }
      }
    }
  }
  
  void _cleanupCollapsedNodeHeights() {
    final keysToRemove = <String>[];
    for (final nodeId in _measuredChildrenHeights.keys) {
      if (!widget.expandedNodes.contains(nodeId)) {
        keysToRemove.add(nodeId);
      }
    }
    for (final key in keysToRemove) {
      _measuredChildrenHeights.remove(key);
    }
    if (keysToRemove.isNotEmpty) {
      setState(() {});
    }
  }
  
  List<String> _getPathToNode(NSLNode node, String targetId, [List<String>? currentPath]) {
    currentPath ??= [];
    currentPath.add(node.id);
    if (node.id == targetId) {
      return List.from(currentPath);
    }
    for (var child in node.children) {
      final path = _getPathToNode(child, targetId, List.from(currentPath));
      if (path.isNotEmpty) {
        return path;
      }
    }
    return [];
  }
  
  bool _isNodeInHighlightedPath(String nodeId) {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return false;
    final path = _getPathToNode(widget.rootNode, selectedId);
    return path.contains(nodeId);
  }
  
  bool _shouldHighlightConnection(String parentId, String childId) {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return false;
    final path = _getPathToNode(widget.rootNode, selectedId);
    final parentIndex = path.indexOf(parentId);
    final childIndex = path.indexOf(childId);
    return parentIndex != -1 && childIndex != -1 && childIndex == parentIndex + 1;
  }
  
  bool _shouldHighlightNodeConnectingLine(String nodeId) {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return false;
    
    final path = _getPathToNode(widget.rootNode, selectedId);
    final nodeIndex = path.indexOf(nodeId);
    
    if (nodeIndex == -1 || nodeIndex >= path.length - 1) return false;
    
    final node = _findNodeById(widget.rootNode, nodeId);
    if (node == null) return false;
    
    final nextNodeId = path[nodeIndex + 1];
    return node.children.any((child) => child.id == nextNodeId);
  }
  
  void _handleNodeInfoTap(String nodeId) {
    setState(() {
      _localSelectedNodeId = nodeId;
    });
    widget.onNodeInfoTap(nodeId);
  }
  
  void _navigateToDetailScreen(NSLHierarchyData1 nodeData) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => NSLMobileNodeDetailScreen(
          nodeData: nodeData,
          onBack: () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          padding: const EdgeInsets.all(14.0),
          child: IntrinsicWidth(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildNodeTree(widget.rootNode, 0, null),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildNodeTree(NSLNode node, int depth, String? parentId) {
    final isExpanded = widget.expandedNodes.contains(node.id);
    final hasChildren = node.children.isNotEmpty;
    final isInHighlightedPath = _isNodeInHighlightedPath(node.id);
    final shouldHighlightConnectionFromParent = parentId != null
        ? _shouldHighlightConnection(parentId, node.id)
        : false;
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    final isSelected = selectedId == node.id;
    final shouldHighlightConnectingLine = _shouldHighlightNodeConnectingLine(node.id);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Current node with potential connection from parent
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Connection from parent (horizontal line)
            if (depth > 0)
              SizedBox(
                width: 40,
                height: 80,
                child: CustomPaint(
                  painter: ParentConnectionPainter(
                    shouldHighlight: shouldHighlightConnectionFromParent,
                  ),
                ),
              ),
            // Node card
            IntrinsicWidth(
              child: NSLMobileNodeCard(
                node: node,
                isExpanded: isExpanded,
                hasChildren: hasChildren,
                onInfoTap: () => _handleNodeInfoTap(node.id),
                onCircleTap: () => _navigateToDetailScreen(node.originalData),
                showConnectingLine: false,
                isHighlighted: isInHighlightedPath,
                isSelected: isSelected,
                shouldHighlightConnectingLine: shouldHighlightConnectingLine,
              ),
            ),
          ],
        ),
        // Animated children connections and nodes
        if (hasChildren) ...[
          _buildAnimatedChildren(node, depth),
        ],
      ],
    );
  }

Widget _buildAnimatedChildren(NSLNode parentNode, int parentDepth) {
  final animation = _animations[parentNode.id];
  if (animation == null) return const SizedBox.shrink();
  
  return AnimatedBuilder(
    animation: animation,
    builder: (context, child) {
      if (animation.value == 0.0) {
        return const SizedBox.shrink();
      }
      
      return SizeTransition(
        sizeFactor: animation,
        axisAlignment: -1.0,
        child: FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, -0.1),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOut,
            )),
            child: _buildChildrenWithConnections(parentNode, parentDepth),
          ),
        ),
      );
    },
  );
}
 Widget _buildChildrenWithConnections(NSLNode parentNode, int parentDepth) {
  final children = parentNode.children;
  if (children.isEmpty) return const SizedBox.shrink();
  
  final isParentExpanded = widget.expandedNodes.contains(parentNode.id);
  final lastChild = children.last;
  final isLastChildExpanded = widget.expandedNodes.contains(lastChild.id);
  final measuredHeight = isParentExpanded
      ? (_measuredChildrenHeights[parentNode.id] ?? 0.0)
      : 0.0;
  
  final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
  int? highlightedChildIndex;
  if (selectedId != null) {
    final path = _getPathToNode(widget.rootNode, selectedId);
    for (int i = 0; i < children.length; i++) {
      if (path.contains(children[i].id)) {
        highlightedChildIndex = i;
        break;
      }
    }
  }
  
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      if (parentDepth > 0) const SizedBox(width: 40),
      const SizedBox(width: 17),
      // Vertical line area
      SizedBox(
        width: 2,
        height: measuredHeight,
        child: CustomPaint(
          painter: ChildrenConnectionPainter(
            childrenCount: children.length,
            shouldHighlight: _isNodeInHighlightedPath(parentNode.id),
            highlightedChildren: children
                .map((child) => _shouldHighlightConnection(parentNode.id, child.id))
                .toList(),
            totalHeight: measuredHeight,
            isLastChildExpanded: isLastChildExpanded,
            highlightedChildIndex: highlightedChildIndex,
          ),
        ),
      ),
      // Animated children column
      SizeReportingWidget(
        onSizeChanged: (height) {
          if (isParentExpanded && _measuredChildrenHeights[parentNode.id] != height) {
            setState(() {
              _measuredChildrenHeights[parentNode.id] = height;
            });
          } else if (!isParentExpanded) {
            setState(() {
              _measuredChildrenHeights.remove(parentNode.id);
            });
          }
        },
        child: IntrinsicWidth(
          child: Column(
            children: children.asMap().entries.map((entry) {
              final index = entry.key;
              final child = entry.value;
              
              return AnimatedBuilder(
                animation: _animationControllers[parentNode.id]!,
                builder: (context, childWidget) {
                  // Fixed timing: Each child starts 80ms after previous, with 400ms duration
                  // This ensures consistent timing regardless of children count
                  final delayPerChild = 0.1; // 80ms delay (10% of 800ms total)
                  final childDuration = 0.4; // 320ms duration (40% of 800ms total)
                  
                  // Calculate start time, but cap it to ensure we don't exceed safe limits
                  double startTime = index * delayPerChild;
                  
                  // If we have many children, cap the start time to leave room for animation
                  if (startTime > 0.5) {
                    startTime = 0.5; // All children after 5th start at same time
                  }
                  
                  // Calculate end time, ensuring it doesn't exceed 1.0
                  double endTime = startTime + childDuration;
                  if (endTime > 1.0) {
                    endTime = 1.0;
                  }
                  
                  // Final safety check to prevent assertion failures
                  if (startTime >= endTime) {
                    startTime = math.max(0.0, endTime - 0.1);
                  }
                  
                  final animationValue = CurvedAnimation(
                    parent: _animationControllers[parentNode.id]!,
                    curve: Interval(
                      startTime,
                      endTime,
                      curve: Curves.easeOut,
                    ),
                  ).value;
                  
                  return Opacity(
                    opacity: animationValue,
                    child: Transform.translate(
                      offset: Offset(0, (1 - animationValue) * 20), // Slide up effect
                      child: childWidget,
                    ),
                  );
                },
                child: Padding(
                  padding: EdgeInsets.only(
                    bottom: index < children.length - 1 ? 8 : 0,
                    top: index == 0 ? 12 : 0,
                  ),
                  child: _buildNodeTree(child, parentDepth + 1, parentNode.id),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    ],
  );
}
  bool _isLastChild(NSLNode node, String? parentId) {
    if (parentId == null) return true;
    NSLNode? parent = _findNodeById(widget.rootNode, parentId);
    if (parent == null) return true;
    return parent.children.isNotEmpty && parent.children.last.id == node.id;
  }
  
  NSLNode? _findNodeById(NSLNode node, String id) {
    if (node.id == id) return node;
    for (var child in node.children) {
      final found = _findNodeById(child, id);
      if (found != null) return found;
    }
    return null;
  }
}

// Enhanced node card with entrance animation
class AnimatedNodeCard extends StatefulWidget {
  final NSLNode node;
  final bool isExpanded;
  final bool hasChildren;
  final VoidCallback onInfoTap;
  final VoidCallback onCircleTap;
  final bool showConnectingLine;
  final bool isHighlighted;
  final bool isSelected;
  final bool shouldHighlightConnectingLine;
  final int animationDelay;
  
  const AnimatedNodeCard({
    super.key,
    required this.node,
    required this.isExpanded,
    required this.hasChildren,
    required this.onInfoTap,
    required this.onCircleTap,
    this.showConnectingLine = false,
    this.isHighlighted = false,
    this.isSelected = false,
    this.shouldHighlightConnectingLine = false,
    this.animationDelay = 0,
  });
  
  @override
  State<AnimatedNodeCard> createState() => _AnimatedNodeCardState();
}

class _AnimatedNodeCardState extends State<AnimatedNodeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
    
    // Start animation with delay
    Future.delayed(Duration(milliseconds: widget.animationDelay), () {
      if (mounted) {
        _controller.forward();
      }
    });
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: NSLMobileNodeCard(
              node: widget.node,
              isExpanded: widget.isExpanded,
              hasChildren: widget.hasChildren,
              onInfoTap: widget.onInfoTap,
              onCircleTap: widget.onCircleTap,
              showConnectingLine: widget.showConnectingLine,
              isHighlighted: widget.isHighlighted,
              isSelected: widget.isSelected,
              shouldHighlightConnectingLine: widget.shouldHighlightConnectingLine,
            ),
          ),
        );
      },
    );
  }
}

// Rest of the painters remain the same
class ParentConnectionPainter extends CustomPainter {
  final bool shouldHighlight;
  final double leftShift;
  
  ParentConnectionPainter({
    required this.shouldHighlight,
    this.leftShift = 2.0,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke
      ..color = shouldHighlight ? const Color(0xFF0058FF) : const Color(0xFFB4B4B4);
    
    final fillPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = shouldHighlight ? const Color(0xFF0058FF) : const Color(0xFFB4B4B4);
    
    final path = Path();
    path.moveTo(-leftShift, 0);
    path.quadraticBezierTo(
      -leftShift,
      size.height * 0.2,
      size.width - 10 - leftShift,
      size.height * 0.2,
    );
    canvas.drawPath(path, paint);
    
    const arrowWidth = 6.0;
    const arrowHeight = 6.0;
    final arrowTip = Offset(size.width - leftShift, size.height * 0.2);
    final arrowPath = Path();
    arrowPath.moveTo(arrowTip.dx, arrowTip.dy);
    arrowPath.lineTo(arrowTip.dx - arrowWidth, arrowTip.dy - arrowHeight / 2);
    arrowPath.lineTo(arrowTip.dx - arrowWidth, arrowTip.dy + arrowHeight / 2);
    arrowPath.close();
    canvas.drawPath(arrowPath, fillPaint);
  }
  
  @override
  bool shouldRepaint(ParentConnectionPainter oldDelegate) {
    return oldDelegate.shouldHighlight != shouldHighlight ||
        oldDelegate.leftShift != leftShift;
  }
}

class ChildrenConnectionPainter extends CustomPainter {
  final int childrenCount;
  final bool shouldHighlight;
  final List<bool> highlightedChildren;
  final double totalHeight;
  final bool isLastChildExpanded;
  final int? highlightedChildIndex;
  
  ChildrenConnectionPainter({
    required this.childrenCount,
    required this.shouldHighlight,
    required this.highlightedChildren,
    required this.totalHeight,
    required this.isLastChildExpanded,
    this.highlightedChildIndex,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    const double verticalLineX = 0;
    const double nodeHeight = 80.0;
    const double spacing = 8.0;
    const double childCircleCenterY = 80.0;
    
    final double lastDirectChildCenterY = totalHeight > 0
        ? (totalHeight - childCircleCenterY).toDouble()
        : (((childrenCount - 1) * (nodeHeight + spacing)) + childCircleCenterY).toDouble();
    
    final double lastChildCenterY = ((childrenCount - 1) * (nodeHeight + spacing)) + childCircleCenterY;
    final double lineEndY = isLastChildExpanded ? lastChildCenterY : lastDirectChildCenterY;
    
    if (highlightedChildIndex != null && shouldHighlight) {
      double highlightedChildCenterY = 12.0;
      highlightedChildCenterY += (highlightedChildIndex! * (nodeHeight + spacing));
      highlightedChildCenterY += 16.0;
      
      final double highlightEndY = highlightedChildCenterY;
      
      paint.color = const Color(0xFF0058FF);
      canvas.drawLine(
        Offset(verticalLineX, 0),
        Offset(verticalLineX, highlightEndY),
        paint,
      );
      
      if (highlightEndY < lineEndY && highlightedChildIndex! < childrenCount - 1) {
        paint.color = const Color(0xFFB4B4B4);
        canvas.drawLine(
          Offset(verticalLineX, highlightEndY),
          Offset(verticalLineX, lineEndY),
          paint,
        );
      }
    } else {
      paint.color = const Color(0xFFB4B4B4);
      canvas.drawLine(
        Offset(verticalLineX, 0),
        Offset(verticalLineX, lineEndY),
        paint,
      );
    }
  }
  
  @override
  bool shouldRepaint(ChildrenConnectionPainter oldDelegate) {
    return oldDelegate.shouldHighlight != shouldHighlight ||
        oldDelegate.childrenCount != childrenCount ||
        oldDelegate.highlightedChildren != highlightedChildren ||
        oldDelegate.totalHeight != totalHeight ||
        oldDelegate.isLastChildExpanded != isLastChildExpanded ||
        oldDelegate.highlightedChildIndex != highlightedChildIndex;
  }
}

class SizeReportingWidget extends StatefulWidget {
  final Widget child;
  final ValueChanged<double> onSizeChanged;
  
  const SizeReportingWidget({
    super.key,
    required this.child,
    required this.onSizeChanged,
  });
  
  @override
  State<SizeReportingWidget> createState() => _SizeReportingWidgetState();
}

class _SizeReportingWidgetState extends State<SizeReportingWidget> {
  final _key = GlobalKey();
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _reportSize());
  }
  
  @override
  void didUpdateWidget(covariant SizeReportingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    WidgetsBinding.instance.addPostFrameCallback((_) => _reportSize());
  }
  
  void _reportSize() {
    final context = _key.currentContext;
    if (context != null) {
      final size = context.size;
      if (size != null) {
        widget.onSizeChanged(size.height);
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      key: _key,
      child: widget.child,
    );
  }
}
