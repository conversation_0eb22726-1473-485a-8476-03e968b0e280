import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/ai_object_right_panel.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/screens/web/static_flow/create_object_screen_static.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/theme/app_colors.dart';

void main() {
  runApp(const MaterialApp(home: AiObjectScreenStatic()));
}

/// Constants for the AI Object Screen Static layout
class _LayoutConstants {
  static const double leftColumnWidthExpanded =
      0.25; // 25% when right panel is expanded
  static const double middleColumnWidthExpanded =
      0.5; // 50% when right panel is expanded
  static const double leftColumnWidthCollapsed =
      0.5; // 50% when right panel is collapsed
  static const double middleColumnWidthCollapsed =
      0.5; // 50% when right panel is collapsed

  static const double minColumnWidth = 0.3; // 30% minimum
  static const double maxMiddleColumnWidth =
      0.7; // 70% maximum for middle column

  static const double gripWidth = 12.0;
  static const double gripHeight = 80.0;
  static const double gripLineWidth = 6.0;
  static const double gripLineHeight = 1.5;
  static const int gripLineCount = 3;
}

/// Colors used in the AI Object Screen Static
class _ScreenColors {
  static const Color middlePanelBackground = Color(0xFFF7F9FB);
  static const Color borderColor = AppColors.greyBorder;
  static const Color gripLineColor = Color(0xFF666666);
  static const Color hoverBackground = Color(0xFFE4EDFF);
  static const Color primaryBlue = AppColors.primaryBlue;
}

class AiObjectScreenStatic extends StatefulWidget {
  const AiObjectScreenStatic({super.key});

  @override
  State<AiObjectScreenStatic> createState() => _AiObjectScreenStaticState();
}

class _AiObjectScreenStaticState extends State<AiObjectScreenStatic> {
  bool _isRightPanelExpanded = true;
  double _leftColumnWidth = _LayoutConstants.leftColumnWidthCollapsed;
  double _middleColumnWidth = _LayoutConstants.middleColumnWidthCollapsed;

  @override
  void initState() {
    super.initState();
    _updateColumnWidths();
  }

  /// Updates column widths based on right panel state
  void _updateColumnWidths() {
    if (_isRightPanelExpanded) {
      _leftColumnWidth = _LayoutConstants.leftColumnWidthExpanded;
      _middleColumnWidth = _LayoutConstants.middleColumnWidthExpanded;
    } else {
      _leftColumnWidth = _LayoutConstants.leftColumnWidthCollapsed;
      _middleColumnWidth = _LayoutConstants.middleColumnWidthCollapsed;
    }
  }

  /// Handles resize gesture for column width adjustment
  void _handleResize(double delta, double containerWidth) {
    if (!_isRightPanelExpanded && containerWidth > 0) {
      setState(() {
        // Calculate new widths based on drag delta
        // Since grip is on the right edge of middle panel:
        // - Drag LEFT (negative delta) = EXPAND middle panel (increase middle width)
        // - Drag RIGHT (positive delta) = SHRINK middle panel (decrease middle width)
        double newMiddleWidth = _middleColumnWidth - (delta / containerWidth);

        // Enforce constraints: middle panel 50% to 70%
        newMiddleWidth =
            newMiddleWidth.clamp(0.5, _LayoutConstants.maxMiddleColumnWidth);

        // Calculate corresponding left column width
        double newLeftWidth = 1.0 - newMiddleWidth;

        // Ensure left column stays within bounds (30% to 50%)
        if (newLeftWidth >= _LayoutConstants.minColumnWidth &&
            newLeftWidth <= 0.5) {
          _middleColumnWidth = newMiddleWidth;
          _leftColumnWidth = newLeftWidth;
        }
      });
    }
  }

  /// Toggles the right panel expanded state
  void _toggleRightPanel() {
    setState(() {
      _isRightPanelExpanded = !_isRightPanelExpanded;
      _updateColumnWidths();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildMainContent(),
        ],
      ),
    );
  }

  /// Builds the header section with navigation and toggle button
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.xs,
      ),
      child: Row(
        children: [
          // Spacer to center the HoverNavItems
          const Expanded(child: SizedBox.shrink()),
          // Centered HoverNavItems
          const HoverNavItems(),
          // Spacer and toggle button at the right end
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _HoverExpandButton(
                  isExpanded: _isRightPanelExpanded,
                  onTap: _toggleRightPanel,
                  tooltipMessage:
                      _isRightPanelExpanded ? 'Collapse panel' : 'Expand panel',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the main content with 3-column layout
  Widget _buildMainContent() {
    return Expanded(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLeftColumn(constraints),
              _buildMiddleColumn(constraints),
              if (_isRightPanelExpanded) _buildRightColumn(),
            ],
          );
        },
      ),
    );
  }

  /// Builds the left column (Create Object Screen)
  Widget _buildLeftColumn(BoxConstraints constraints) {
    return SizedBox(
      width: constraints.maxWidth * _leftColumnWidth,
      child: const Stack(
        children: [
          CreateObjectScreenStatic(),
          // NotificationHoverPanel(),
        ],
      ),
    );
  }

  /// Builds the middle column with optional resize grip
  Widget _buildMiddleColumn(BoxConstraints constraints) {
    return SizedBox(
      width: constraints.maxWidth * _middleColumnWidth,
      child: Stack(
        children: [
          _buildMiddlePanelContent(),
          if (!_isRightPanelExpanded) _buildResizeGrip(constraints),
        ],
      ),
    );
  }

  /// Builds the main middle panel content
  Widget _buildMiddlePanelContent() {
    return Container(
      decoration: const BoxDecoration(
        color: _ScreenColors.middlePanelBackground,
        border: Border(
          left: BorderSide(color: _ScreenColors.borderColor, width: 1),
          right: BorderSide(color: _ScreenColors.borderColor, width: 1),
        ),
      ),
      child: const Center(
        child: ExtractDetailsMiddleStatic(),
      ),
    );
  }

  /// Builds the resize grip for column adjustment
  Widget _buildResizeGrip(BoxConstraints constraints) {
    return Positioned(
      left: -AppSpacing.xxs,
      top: 0,
      bottom: 0,
      child: Center(
        child: MouseRegion(
          cursor: SystemMouseCursors.resizeColumn,
          child: GestureDetector(
            onPanUpdate: (details) {
              _handleResize(details.delta.dx, constraints.maxWidth);
            },
            child: Container(
              width: _LayoutConstants.gripWidth,
              height: _LayoutConstants.gripHeight,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppSpacing.size6),
                border: Border.all(
                  color: _ScreenColors.borderColor,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: AppSpacing.xxs,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (int i = 0; i < _LayoutConstants.gripLineCount; i++) ...[
                    Container(
                      width: _LayoutConstants.gripLineWidth,
                      height: _LayoutConstants.gripLineHeight,
                      decoration: BoxDecoration(
                        color: _ScreenColors.gripLineColor,
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                    if (i < _LayoutConstants.gripLineCount - 1)
                      SizedBox(height: AppSpacing.xxs - 1),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the right column (AI Object Right Panel)
  Widget _buildRightColumn() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(left: AppSpacing.md),
        child: const AiObjectRightPanel(),
      ),
    );
  }
}

/// Expand/collapse button with hover effect for right panel
class _HoverExpandButton extends StatefulWidget {
  final bool isExpanded;
  final VoidCallback onTap;
  final String tooltipMessage;

  const _HoverExpandButton({
    required this.isExpanded,
    required this.onTap,
    this.tooltipMessage = '',
  });

  @override
  State<_HoverExpandButton> createState() => _HoverExpandButtonState();
}

class _HoverExpandButtonState extends State<_HoverExpandButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Tooltip(
        message: widget.tooltipMessage,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            width: AppSpacing.lg,
            height: AppSpacing.lg,
            decoration: BoxDecoration(
              color: _isHovered
                  ? _ScreenColors.hoverBackground
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
            ),
            child: Center(
              child: Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..scale(widget.isExpanded ? 1.0 : -1.0, 1.0, 1.0),
                child: Icon(
                  Icons.login,
                  size: AppSpacing.size20,
                  color: _isHovered
                      ? _ScreenColors.primaryBlue
                      : Colors.grey.shade600,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
