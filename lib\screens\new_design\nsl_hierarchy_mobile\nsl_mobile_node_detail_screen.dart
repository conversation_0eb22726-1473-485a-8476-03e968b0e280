import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/nsl_hierarchy_provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/services/dummy_consolidated_service.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/speech_bubble_container.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/go_list_drawer.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/lo_list_drawer.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class NSLMobileNodeDetailScreen extends StatefulWidget {
  final NSLHierarchyData1 nodeData;
  final VoidCallback onBack;

  const NSLMobileNodeDetailScreen({
    super.key,
    required this.nodeData,
    required this.onBack,
  });

  @override
  State<NSLMobileNodeDetailScreen> createState() =>
      _NSLMobileNodeDetailScreenState();
}

class _NSLMobileNodeDetailScreenState extends State<NSLMobileNodeDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isTransactionsExpanded = false;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;

  // Loading states
  bool _isLoadingNodeDetails = false;
  bool _isLoadingTransactions = false;

  // Data
  NslTreeSidePanel? _nodeDetails;
  MetricsInfo? _nodeTransactions;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _selectedFromDate = DateTime.now().subtract(const Duration(days: 30));
    _selectedToDate = DateTime.now();
    _loadNodeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNodeData() async {
    final provider = Provider.of<NslHierarchyProvider>(context, listen: false);

    try {
      // First load node details (GO/LO counts and lists)
      await provider.onNodeTitleTap(NSLNode(
        id: widget.nodeData.id,
        title: widget.nodeData.title,
        type: widget.nodeData.type,
        totalBets: widget.nodeData.totalBets,
        betBreakdown: widget.nodeData.betBreakdown,
        financialSummary: widget.nodeData.financialSummary,
        levelName: widget.nodeData.levelName,
        level: widget.nodeData.level,
        originalData: widget.nodeData,
        children: [],
        isExpanded: false,
      ));

      // Then load transactions data with date range
      await provider.onDateRangeChanged(_selectedFromDate!, _selectedToDate!);
    } catch (e) {
      print('Error loading node data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NslHierarchyProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          drawer: const CustomDrawer(),
          endDrawer: null, // Will be set dynamically when needed
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.transparent,
            foregroundColor: Colors.black,
            elevation: 6,
            automaticallyImplyLeading: false,
            titleSpacing: 0,
            title: Column(
              children: [
                Row(
                  children: [
                    // Hamburger menu icon
                    Builder(
                      builder: (context) => IconButton(
                        icon: const Icon(Icons.menu,
                            color: Colors.black, size: 24),
                        onPressed: () => Scaffold.of(context).openDrawer(),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                      ),
                    ),
                  ],
                ),
                Divider(
                  height: 2,
                )
              ],
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                _buildHeader(),
                _buildDateSelection(),
                _buildLatestTransactionsContainer(),
                _buildTabNavigation(),
                _buildScrollableTabContent(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Node circle and speech bubble
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Node circle
                Column(
                  children: [
                    Container(
                      width: 34,
                      height: 34,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: widget.nodeData.level == 'M4'
                              ? const Color(0xFF0058FF)
                              : NSLNode.getLevelColorMobile(
                                  widget.nodeData.level),
                          width: 3,
                        ),
                      ),
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: widget.nodeData.level == 'M4'
                              ? const Color(0xFF0058FF)
                              : NSLNode.getLevelColorMobile(
                                  widget.nodeData.level),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            widget.nodeData.level,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: AppColors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: AppSpacing.xs),
                    GestureDetector(
                      onTap: widget.onBack,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Color(0x26000000),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.arrow_back,
                          size: 20,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(width: 12),

                // Speech bubble with node info
                IntrinsicWidth(
                  child: SpeechBubbleContainer(
                    tailOffset: -20.0,
                    backgroundColor: const Color(0xFF0058FF),
                    borderColor: const Color(0xFF0058FF),
                    borderWidth: 2.0,
                    showTail: true,
                    tailDirection: TailDirection.left,
                    tailSize: 12.0,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title section
                        GestureDetector(
                          onTap: widget.onBack,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 16),
                            child: Text(
                              // widget.nodeData.title.length > 20
                              //     ? '${widget.nodeData.title.substring(0, 15)}...'
                                  // :
                                   widget.nodeData.title,
                              textAlign: TextAlign.center,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.titleLarge(context),
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: AppColors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),

                        // ID and NP row
                        Container(
                          margin: const EdgeInsets.only(
                              left: 1, right: 1, bottom: 1),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 12),
                          decoration: BoxDecoration(
                            color: Color(0xFFCBDDFF),
                            //NSLNode.getLevelColor(widget.nodeData.level),
                            border: Border.all(
                              color: Colors.grey.shade300,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'ID: ${widget.nodeData.employeeId ?? widget.nodeData.id}',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                'NP: ${widget.nodeData.totalBets}',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // // Back button
          // GestureDetector(
          //   onTap: widget.onBack,
          //   child: Container(
          //     padding: const EdgeInsets.all(8),
          //     decoration: BoxDecoration(
          //       color: Color(0x26000000),
          //       borderRadius: BorderRadius.circular(20),
          //     ),
          //     child: const Icon(
          //       Icons.arrow_back,
          //       size: 20,
          //       color: Colors.black,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

// Replace the _buildDateSelection() method with this updated version:

  Widget _buildDateSelection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Divider(height: 2, color: Color(0xFFE3E3E3)),
          SizedBox(height: 5),

          // Single container for the date range
          GestureDetector(
            onTap: () => _selectDateRange(context),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
              ),
              // decoration: BoxDecoration(
              //   color: Colors.white,
              //   border: Border.all(color: const Color(0xFFB4B4B4)),
              //   borderRadius: BorderRadius.circular(4),
              // ),
              child: Row(
                children: [
                  Container(
                      height: 24,
                      width: 24,
                      decoration: BoxDecoration(
                        color: Color(0x0D000000),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.calendar_today_outlined,
                          size: 14, color: Colors.black)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${_formatDateWithDay(_selectedFromDate)} ',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    '-   ',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Container(
                      height: 24,
                      width: 24,
                      decoration: BoxDecoration(
                        color: Color(0x0D000000),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.calendar_today_outlined,
                          size: 14, color: Colors.black)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      ' ${_formatDateWithDay(_selectedToDate)}',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 5),
          Divider(height: 2, color: Color(0xFFE3E3E3)),
        ],
      ),
    );
  }

// Add these new helper methods to your class:

  String _formatDateWithDay(DateTime? date) {
    if (date == null) return 'DD/MM/YY';

    final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];

    return '${dayNames[date.weekday - 1]}, ${date.day} ${monthNames[date.month - 1]} ${date.year}';
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      initialDateRange: DateTimeRange(
        start: _selectedFromDate ??
            DateTime.now().subtract(const Duration(days: 30)),
        end: _selectedToDate ?? DateTime.now(),
      ),
    );

    if (picked != null) {
      setState(() {
        _selectedFromDate = picked.start;
        _selectedToDate = picked.end;
      });

      // Reload data with new date range - preserving your existing functionality
      await _loadNodeData();
    }
  }

// - _selectFromDate()
// - _selectToDate()
  Widget _buildLatestTransactionsContainer() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(0xFFEBF5FF),
        // border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            padding:
                const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Latest Transactions',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleLarge(context),
                    fontFamily: FontManager.fontFamilyInter,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isTransactionsExpanded = !_isTransactionsExpanded;
                    });
                  },
                  child: Text(
                    _isTransactionsExpanded ? 'View Back' : 'See all',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: const Color(0xFF0058FF),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Metrics cards
          if (!_isTransactionsExpanded)
            _buildCompactMetrics()
          else
            _buildExpandedMetrics(),

          // GO/LO row
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Consumer<NslHierarchyProvider>(
                    builder: (context, provider, child) {
                      return _buildGoLoCard(
                        'Total GO\'s',
                        provider.isLoadingNodeDetails
                            ? null
                            : provider.nodeDetails?.result?.goCount
                                    ?.toString() ??
                                '0',
                        provider.isLoadingNodeDetails,
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Consumer<NslHierarchyProvider>(
                    builder: (context, provider, child) {
                      return _buildGoLoCard(
                        'Total LO\'s',
                        provider.isLoadingNodeDetails
                            ? null
                            : provider.nodeDetails?.result?.loCount
                                    ?.toString() ??
                                '0',
                        provider.isLoadingNodeDetails,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactMetrics() {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = screenWidth * 0.36;
    return Consumer<NslHierarchyProvider>(
      builder: (context, provider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                SizedBox(
                  width: cardWidth,
                  child: _buildMetricCard(
                    'Total Transactions',
                    provider.isLoadingTransactions
                        ? null
                        : _formatTransactionCount(provider
                                .nodeTransactions?.result?.totalTransactions ??
                            0),
                    provider.isLoadingTransactions,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: cardWidth,
                  child: _buildMetricCard(
                    _getChildNodeTitle(),
                   _getChildNodeCount().toString(),
                   false,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: cardWidth,
                  child: _buildMetricCard(
                    'Revenue',
                    provider.isLoadingTransactions
                        ? null
                        : _formatNumber(provider
                                .nodeTransactions?.result?.revenue
                                ?.toDouble() ??
                            0),
                    provider.isLoadingTransactions,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: cardWidth,
                  child: _buildMetricCard(
                    'Cost',
                    provider.isLoadingTransactions
                        ? null
                        : _formatNumber(
                            provider.nodeTransactions?.result?.cost ?? 0),
                    provider.isLoadingTransactions,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: cardWidth,
                  child: _buildMetricCard(
                    'Margin',
                    provider.isLoadingTransactions
                        ? null
                        : '${(provider.nodeTransactions?.result?.margin ?? 0).toStringAsFixed(1)}%',
                    provider.isLoadingTransactions,
                  ),
                ),

                // Add more metric cards as needed
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildExpandedMetrics() {
    return Consumer<NslHierarchyProvider>(builder: (context, provider, child) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            // First row - no animation (these are already visible in compact mode)
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Total Transactions',
                    provider.isLoadingTransactions
                        ? null
                        : _formatTransactionCount(provider
                                .nodeTransactions?.result?.totalTransactions ??
                            0),
                    provider.isLoadingTransactions,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildMetricCard(
                    _getChildNodeTitle(),
                   _getChildNodeCount().toString(),
                    false,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Second row - animate from right (these are new cards)
            _buildAnimatedMetricRow([
              _buildMetricCard(
                'Revenue',
                // _formatNumber(provider.nodeTransactions?.result?.revenue!);
                provider.isLoadingTransactions
                    ? null
                    : _formatNumber(provider.nodeTransactions?.result?.revenue
                            ?.toDouble() ??
                        0),
                provider.isLoadingTransactions,
              ),
              _buildMetricCard(
                'Cost',
                provider.isLoadingTransactions
                    ? null
                    : _formatNumber(
                        provider.nodeTransactions?.result?.cost ?? 0),
                provider.isLoadingTransactions,
              ),
            ], 0),
            const SizedBox(height: 8),
            // Third row - animate from right (this is a new card)
            _buildAnimatedMetricRow([
              _buildMetricCard(
                'Margin',
                provider.isLoadingTransactions
                    ? null
                    : '${(provider.nodeTransactions?.result?.margin ?? 0).toStringAsFixed(1)}%',
                provider.isLoadingTransactions,
              ),
              const SizedBox(), // Empty space for the second card
            ], 1),
          ],
        ),
      );
    });
  }

  Widget _buildAnimatedMetricRow(List<Widget> cards, int rowIndex) {
    return TweenAnimationBuilder<double>(
      key: ValueKey('animated_row_$rowIndex'),
      duration: Duration(milliseconds: 400 + (rowIndex * 150)),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset((1 - value) * 120, (1 - value) * -30),
          child: Opacity(
            opacity: value,
            child: Row(
              children: [
                if (cards.isNotEmpty) Expanded(child: cards[0]),
                const SizedBox(width: 8),
                if (cards.length > 1)
                  Expanded(child: cards[1])
                else
                  const Expanded(child: SizedBox()),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricCard(String title, String? value, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
         boxShadow: [
            BoxShadow(
              color: Color(0x1F000000),
              blurRadius: 1,
              offset: const Offset(0, 1),
            ),
          ],
      ),
      
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (isLoading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          else
            Text(
              value ?? 'N/A',
              style: FontManager.getCustomStyle(
                fontSize: 20,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          const SizedBox(height: 4),
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelMedium(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoLoCard(String title, String? value, bool isLoading) {
    return GestureDetector(
      onTap: () => _openRightDrawer(title),
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.white, width: 0.5),
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: Color(0x1F000000),
              blurRadius: 1,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontFamily: FontManager.fontFamilyInter,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                // const SizedBox(height: 4),
                if (isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  Text(
                    value ?? 'N/A',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
            const Icon(
              Icons.arrow_forward,
              color: Color(0xFF0058FF),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _openRightDrawer(String title) {
    final provider = Provider.of<NslHierarchyProvider>(context, listen: false);

    // The provider already has the selected node ID set from _loadNodeData()
    // The drawers will use provider.selectedNodeId to fetch data for the correct node

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Align(
          alignment: Alignment.centerRight,
          child: Material(
            color: Colors.transparent,
            child: title.contains('GO')
                ? GoListDrawer(
                    onClose: () {
                      Navigator.of(context).pop();
                      // Clear the GO/LO panel state when closing (same as modern side panel)
                      provider.clearArrowSelection();
                    },
                  )
                : LoListDrawer(
                    onClose: () {
                      Navigator.of(context).pop();
                      // Clear the GO/LO panel state when closing (same as modern side panel)
                      provider.clearArrowSelection();
                    },
                  ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          )),
          child: child,
        );
      },
    );
  }

  Widget _buildTabNavigation() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFEBF5FF),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        indicatorSize:
            TabBarIndicatorSize.tab, // Ensures indicator matches tab width
        labelPadding: EdgeInsets.zero, // Remove label padding
        controller: _tabController,
        dividerColor: Colors.transparent,
        indicator: BoxDecoration(
          color: const Color(0xFF0058FF),
          borderRadius: BorderRadius.circular(12),
        ),
        labelStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelLarge(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelLarge(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Color(0xFF373737),
          fontWeight: FontWeight.w400,
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Color(0xFF373737),
        tabs: const [
          Tab(text: 'Standalone'),
          Tab(text: 'Consolidated'),
          Tab(text: 'BET Breakdown'),
        ],
      ),
    );
  }

  Widget _buildScrollableTabContent() {
    return AnimatedBuilder(
      animation: _tabController,
      builder: (context, child) {
        // Get the current tab index
        int currentIndex = _tabController.index;

        // Return the content for the current tab
        switch (currentIndex) {
          case 0:
            return _buildStandaloneTabContent();
          case 1:
            return _buildConsolidatedTabContent();
          case 2:
            return SizedBox();
          //  _buildBetBreakdownTabContent();
          default:
            return _buildStandaloneTabContent();
        }
      },
    );
  }

  Widget _buildStandaloneTabContent() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 12, bottom: 12, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFinancialCards(),
          const SizedBox(height: 12),
          _buildIncomeStatement(),
          const SizedBox(height: 12),
          _buildBalanceSheet(),
        ],
      ),
    );
  }

  Widget _buildConsolidatedTabContent() {
    return FutureBuilder<List<dynamic>>(
      future: DummyConsolidatedService.loadDummyData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Padding(
            padding: EdgeInsets.all(20),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: Text(
                'Error loading consolidated data',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          );
        }

        final dataList = snapshot.data ?? [];
        Map<String, dynamic>? nodeData;

        for (var item in dataList) {
          if (item is Map<String, dynamic> &&
              item['id'] == widget.nodeData.id) {
            nodeData = item;
            break;
          }
        }

        String totalGo = nodeData?['consolidated']?['total_gos'] ?? '45';
        String internalElimination =
            nodeData?['consolidated']?['internal_elimination'] ?? '134';
        String efficiency = nodeData?['consolidated']?['effeciency'] ?? '88.5%';
        String teamCoordination =
            nodeData?['consolidated']?['team_coordination'] ?? '39.2%';

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard(
                        'Total Go', totalGo, Colors.green),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard('Internal Elimination',
                        internalElimination, Colors.blue),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard(
                        'Efficiency', efficiency, Colors.orange),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard(
                        'Team Coordination', teamCoordination, Colors.purple),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBetBreakdownTabContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BET Breakdown',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildBetBreakdownCard(
              'GO\'s', widget.nodeData.betBreakdown.gos.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard(
              'LO\'s', widget.nodeData.betBreakdown.los.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('NP Functions',
              widget.nodeData.betBreakdown.npFunctions.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Input/Output Stacks',
              widget.nodeData.betBreakdown.inputOutputStacks.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Subordinate NSL',
              widget.nodeData.betBreakdown.subordinateNsl.toString()),
        ],
      ),
    );
  }

  Widget _buildFinancialCards() {
    return Consumer<NslHierarchyProvider>(builder: (context, provider, child) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildFinancialCard(
                  'Total Revenue',
                  provider.isLoadingTransactions
                      ? null
                      : _formatNumber(provider.nodeTransactions?.result?.revenue
                              ?.toDouble() ??
                          0),
                  '+12.5% vs last month',
                  Colors.green,
                  provider.isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFinancialCard(
                  'Net Margin',
                  provider.isLoadingTransactions
                      ? null
                      : '${(provider.nodeTransactions?.result?.margin ?? 0).toStringAsFixed(1)}%',
                  '2.1% vs last month',
                  Colors.blue,
                  provider.isLoadingTransactions,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildFinancialCard(
                  'Total Transactions',
                  provider.isLoadingTransactions
                      ? null
                      : _formatTransactionCount(provider
                              .nodeTransactions?.result?.totalTransactions ??
                          0),
                  '+8.7% vs last month',
                  Colors.orange,
                  provider.isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFinancialCard(
                  'Utilization',
                  '88.5%',
                  '+3.2% vs last month',
                  Colors.purple,
                  false,
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  Widget _buildFinancialCard(
      String title, String? value, String trend, Color color, bool isLoading) {
    return Container(
      padding: const EdgeInsets.only(left: 12, right: 12, bottom: 8, top: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFFE8E8E8), width: 0.5),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelLarge(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          if (isLoading)
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            )
          else
            Text(
              value ?? 'N/A',
              style: FontManager.getCustomStyle(
                fontSize: 20,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          const SizedBox(height: AppSpacing.md),
          Text(
            trend,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatement() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0x0A000000),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income Statement - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _getDateRangeText(),
            style: FontManager.getCustomStyle(
              fontSize: 10,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 4),
          const Divider(color: Color(0xFFB4B4B4), height: 0.5),
          // const SizedBox(height: 8),
          _buildIncomeStatementContent(),
        ],
      ),
    );
  }

  Widget _buildIncomeStatementContent() {
    return Consumer<NslHierarchyProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingTransactions) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (provider.nodeTransactions?.result?.pnl == null ||
            provider.nodeTransactions!.result!.pnl!.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Text(
              'No PnL data available',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey.shade600,
              ),
            ),
          );
        }

        return Column(
          children: [
            _buildIncomeStatementHeader(),
            ..._buildIncomeStatementRows(provider),
          ],
        );
      },
    );
  }

  Widget _buildIncomeStatementHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Line Item',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Amount',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildIncomeStatementRows(NslHierarchyProvider provider) {
    final pnlData = provider.nodeTransactions!.result!.pnl!;
    List<Widget> rows = [];

    var incomeSection;
    var expenseSection;

    for (var section in pnlData) {
      if (section.name?.toLowerCase().contains('income') == true) {
        incomeSection = section;
      } else if (section.name?.toLowerCase().contains('expense') == true) {
        expenseSection = section;
      }
    }

    double totalIncome = 0.0;
    double totalExpense = 0.0;
    double totalRevenue = 0.0;

    // Calculate total income (revenue)
    if (incomeSection != null) {
      totalIncome = incomeSection.sum ?? 0.0;
      totalRevenue = totalIncome; // Revenue is the total income
    }

    // Calculate total expense
    if (expenseSection != null) {
      totalExpense = expenseSection.sum ?? 0.0;
    }

    // Build income section
    if (incomeSection != null) {
      rows.add(_buildIncomeStatementRow(
        'Income',
        '',
        '',
        totalRevenue,
        isSubHeader: true,
      ));

      if (incomeSection.details != null) {
        for (int i = 0; i < incomeSection.details!.length; i++) {
          var detail = incomeSection.details![i];
          final amount = detail.value ?? 0.0;
          final percentage =
              totalIncome > 0 ? (amount / totalIncome) * 100 : 0.0;

          rows.add(_buildIncomeStatementRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}% of Revenue',
            totalRevenue,
          ));
          if (i < incomeSection.details!.length - 1) {
            rows.add(_buildDivider());
          }
        }
      }

      rows.add(_buildIncomeStatementRow(
        'Total Income',
        _formatCurrency(totalIncome),
        '100% of Revenue',
        totalRevenue,
        isTotal: true,
      ));
    }

    // Build expense section
    if (expenseSection != null) {
      rows.add(_buildIncomeStatementRow(
        'Expense',
        '',
        '',
        totalRevenue,
        isSubHeader: true,
      ));

      if (expenseSection.details != null) {
        for (int i = 0; i < expenseSection.details!.length; i++) {
          var detail = expenseSection.details![i];
          final amount = detail.value ?? 0.0;
          final percentage =
              totalExpense > 0 ? (amount / totalExpense) * 100 : 0.0;

          rows.add(_buildIncomeStatementRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}% of Total Expense',
            totalRevenue,
          ));
          if (i < expenseSection.details!.length - 1) {
            rows.add(_buildDivider());
          }
        }
      }

      rows.add(_buildIncomeStatementRow(
        'Total Expense',
        _formatCurrency(totalExpense),
        '100% of Total Expense',
        totalRevenue,
        isTotal: true,
      ));
    }

    final netProfit = totalIncome - totalExpense;
    rows.add(_buildIncomeStatementRow(
      'Net Profit',
      _formatCurrency(netProfit),
      '',
      totalRevenue,
      isFinal: true,
    ));

    return rows;
  }

  Widget _buildIncomeStatementRow(
      String item, String amount, String percentage, double totalRevenue,
      {bool isTotal = false, bool isFinal = false, bool isSubHeader = false}) {
    final fontWeight =
        (isTotal || isFinal || isSubHeader) ? FontWeight.w600 : FontWeight.w400;
    final backgroundColor = isTotal
        ? Color(0xFFF0F0F0)
        : (isFinal ? Color(0xFFE8F5E8) : Colors.transparent);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
                fontWeight: fontWeight,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (amount.isNotEmpty)
                  Text(
                    amount,
                    textAlign: TextAlign.right,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                      fontWeight: fontWeight,
                    ),
                  ),
                if (percentage.isNotEmpty && !isSubHeader)
                  Text(
                    percentage,
                    textAlign: TextAlign.right,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context) *
                          0.85, // Slightly smaller font
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  } //   final pnlData = provider.nodeTransactions!.result!.pnl!;

  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Divider(
        color: Color(0xFFB4B4B4),
        // thickness: 0.5,
        height: 1,
      ),
    );
  }

  Widget _buildBalanceSheet() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0x0A000000),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Balance Sheet - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _getDateRangeText(),
            style: FontManager.getCustomStyle(
              fontSize: 10,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 4),
          const Divider(color: Color(0xFFB4B4B4), height: 0.5),
          _buildBalanceSheetContent(),
        ],
      ),
    );
  }

  String _getDateRangeText() {
    if (_selectedFromDate == null || _selectedToDate == null) {
      return 'For the Month Ended December 30, 2024'; // Fallback text
    }

    // Check if the dates are in the same month
    if (_selectedFromDate!.month == _selectedToDate!.month &&
        _selectedFromDate!.year == _selectedToDate!.year) {
      return 'For the Month Ended ${_formatMonthYear(_selectedToDate!)}';
    } else {
      return 'For the Period ${_formatDateForStatement(_selectedFromDate!)} to ${_formatDateForStatement(_selectedToDate!)}';
    }
  }

  String _formatMonthYear(DateTime date) {
    final monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${monthNames[date.month - 1]} ${date.year}';
  }

  String _formatDateForStatement(DateTime date) {
    final monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${monthNames[date.month - 1]} ${date.day}, ${date.year}';
  }

  Widget _buildBalanceSheetContent() {
    return Consumer<NslHierarchyProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingTransactions) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (provider.nodeTransactions?.result?.balanceSheet == null ||
            provider.nodeTransactions!.result!.balanceSheet!.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Text(
              'No Balance Sheet data available',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey.shade600,
              ),
            ),
          );
        }

        return Column(
          children: [
            _buildBalanceSheetHeader(),
            ..._buildBalanceSheetRows(provider),
          ],
        );
      },
    );
  }

  Widget _buildBalanceSheetHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Line Item',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Amount',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBalanceSheetRows(NslHierarchyProvider provider) {
    final balanceSheetData = provider.nodeTransactions!.result!.balanceSheet!;
    List<Widget> rows = [];

    var assetSection;
    var liabilitySection;
    var equitySection;

    for (var section in balanceSheetData) {
      if (section.name?.toLowerCase().contains('asset') == true) {
        assetSection = section;
      } else if (section.name?.toLowerCase().contains('liability') == true) {
        liabilitySection = section;
      } else if (section.name?.toLowerCase().contains('equity') == true) {
        equitySection = section;
      }
    }

    if (assetSection != null) {
      final assetTotal = assetSection.sum ?? 0.0;

      rows.add(_buildBalanceSheetRow(
        'Asset',
        '',
        '',
        isSubHeader: true,
      ));

      if (assetSection.details != null) {
        for (int i = 0; i < assetSection.details!.length; i++) {
          var detail = assetSection.details![i];
          final amount = detail.value ?? 0.0;
          final percentage = assetTotal > 0 ? (amount / assetTotal) * 100 : 0.0;

          rows.add(_buildBalanceSheetRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}% of Total Assets',
          ));

          // Add divider between asset items (but not after the last one)
          if (i < assetSection.details!.length - 1) {
            rows.add(_buildDivider());
          }
        }
      }

      rows.add(_buildBalanceSheetRow(
        'Total Asset',
        _formatCurrency(assetTotal),
        '100% of Total Assets',
        isTotal: true,
      ));
    }

    if (liabilitySection != null) {
      final liabilityTotal = liabilitySection.sum ?? 0.0;

      rows.add(_buildBalanceSheetRow(
        'Liability',
        '',
        '',
        isSubHeader: true,
      ));

      if (liabilitySection.details != null) {
        for (int i = 0; i < liabilitySection.details!.length; i++) {
          var detail = liabilitySection.details![i];
          final amount = detail.value ?? 0.0;
          final percentage =
              liabilityTotal > 0 ? (amount / liabilityTotal) * 100 : 0.0;

          rows.add(_buildBalanceSheetRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}% of Total Liabilities',
          ));

          // Add divider between liability items (but not after the last one)
          if (i < liabilitySection.details!.length - 1) {
            rows.add(_buildDivider());
          }
        }
      }

      rows.add(_buildBalanceSheetRow(
        'Total Liability',
        _formatCurrency(liabilityTotal),
        '100% of Total Liabilities',
        isTotal: true,
      ));
    }
      if (equitySection != null) {
      final equityTotal = equitySection.sum ?? 0.0;
      final liabilityTotal = liabilitySection.sum ?? 0.0;

      rows.add(_buildBalanceSheetRow(
        'Equity',
        '',
        '',
        isSubHeader: true,
      ));

      if (equitySection.details != null) {
        for (int i = 0; i < equitySection.details!.length; i++) {
          var detail = equitySection.details![i];
          final amount = detail.value ?? 0.0;
          final percentage =
              equityTotal > 0 ? (amount / equityTotal) * 100 : 0.0;

          rows.add(_buildBalanceSheetRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}% of Total Equity',
          ));

          // Add divider between liability items (but not after the last one)
          if (i < equitySection.details!.length - 1) {
            rows.add(_buildDivider());
          }
        }
      }

      rows.add(_buildBalanceSheetRow(
        'Total Asset',
        _formatCurrency(equityTotal),
        '100% of Total Equity',
        isTotal: true,
      ));
       rows.add(_buildBalanceSheetRow(
        'Total (Liability + Equity)',
        _formatCurrency(liabilityTotal + equityTotal),
        '',
        isTotal: true,
      ));
    }

    return rows;
  }

  Widget _buildBalanceSheetRow(String item, String amount, String percentage,
      {bool isTotal = false, bool isFinal = false, bool isSubHeader = false}) {
    final fontWeight =
        (isTotal || isFinal || isSubHeader) ? FontWeight.w600 : FontWeight.w400;
    final backgroundColor = isTotal
        ? Color(0xFFF0F0F0)
        : (isFinal ? Color(0xFFE8F5E8) : Colors.transparent);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
                fontWeight: fontWeight,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (amount.isNotEmpty)
                  Text(
                    amount,
                    textAlign: TextAlign.right,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                      fontWeight: fontWeight,
                    ),
                  ),
                if (percentage.isNotEmpty && !isSubHeader)
                  Text(
                    percentage,
                    textAlign: TextAlign.right,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context) *
                          0.85, // Slightly smaller font
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConsolidatedMetricCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.only(left: 12, right: 12, bottom: 8, top: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFFE8E8E8), width: 0.5),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelLarge(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: 20,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBetBreakdownTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BET Breakdown',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildBetBreakdownCard(
              'GO\'s', widget.nodeData.betBreakdown.gos.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard(
              'LO\'s', widget.nodeData.betBreakdown.los.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('NP Functions',
              widget.nodeData.betBreakdown.npFunctions.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Input/Output Stacks',
              widget.nodeData.betBreakdown.inputOutputStacks.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Subordinate NSL',
              widget.nodeData.betBreakdown.subordinateNsl.toString()),
        ],
      ),
    );
  }

  Widget _buildBetBreakdownCard(String title, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for child node information
  String _getChildNodeTitle() {
    switch (widget.nodeData.level) {
      case 'M4':
        return 'M3 Nodes';
      case 'M3':
        return 'M2 Nodes';
      case 'M2':
        return 'M1 Nodes';
      case 'M1':
        return 'Employees'; // M1 nodes have employees as children
      default:
        return 'Child Nodes';
    }
  }

  int _getChildNodeCount() {
        final childrenCount = widget.nodeData.children?.length ?? 0;
    // For now, return a placeholder count
    // In a real implementation, you would get this from the API or provider
    // based on the actual children of this node
    switch (widget.nodeData.level) {
      case 'M4':
        return  childrenCount ?? 0; // M3 nodes count
      case 'M3':
        return childrenCount ?? 0;  // M2 nodes count
      case 'M2':
        return  childrenCount ?? 0; // M1 nodes count
      case 'M1':
        return childrenCount ?? 0; // Employee count
      default:
        return 0;
    }
  }

  // Helper methods
  String _formatDate(DateTime? date) {
    if (date == null) return 'DD/MM/YY';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year.toString().substring(2)}';
  }

  String _formatNumber(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  String _formatTransactionCount(int value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toString();
    }
  }

  String _formatCurrency(double value) {
    if (value >= 1000000) {
      return '\$${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '\$${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return '\$${value.toStringAsFixed(0)}';
    }
  }

  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedFromDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedFromDate) {
      setState(() {
        _selectedFromDate = picked;
      });
      _loadNodeData(); // Reload data with new date range
    }
  }

  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedToDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedToDate) {
      setState(() {
        _selectedToDate = picked;
      });
      _loadNodeData(); // Reload data with new date range
    }
  }
}
