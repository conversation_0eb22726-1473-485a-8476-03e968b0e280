import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'dart:convert';
import '../utils/callback_interpreter.dart';

/// A configurable duration widget that allows users to input hours, minutes, and seconds.
class DurationWidget extends StatefulWidget {
  // Basic properties
  final Duration initialValue;
  final Duration? minDuration;
  final Duration? maxDuration;
  final bool showHours;
  final bool showMinutes;
  final bool showSeconds;
  final bool showMilliseconds;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String? hoursLabel;
  final String? minutesLabel;
  final String? secondsLabel;
  final String? millisecondsLabel;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;
  final bool hasAnimation;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final bool isVertical;
  final bool showSeparators;
  final String separator;

  // Callback
  final Function(Duration)? onChanged;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  const DurationWidget({
    super.key,
    this.initialValue = const Duration(minutes: 5),
    this.minDuration,
    this.maxDuration,
    this.showHours = true,
    this.showMinutes = true,
    this.showSeconds = true,
    this.showMilliseconds = false,
    this.textColor = const Color(0xFF333333),
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.center,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.hoursLabel = 'h',
    this.minutesLabel = 'm',
    this.secondsLabel = 's',
    this.millisecondsLabel = 'ms',
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.hasAnimation = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 0.0, vertical: 0.0),
    this.margin = const EdgeInsets.all(0),
    this.isVertical = false,
    this.showSeparators = true,
    this.separator = ':',
    this.onChanged,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
  });

  /// Creates a DurationWidget from a JSON map
  factory DurationWidget.fromJson(Map<String, dynamic> json) {
    // Parse duration values
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) return const Duration(minutes: 5);

      int hours = 0;
      int minutes = 0;
      int seconds = 0;
      int milliseconds = 0;

      if (durationValue is Map) {
        if (durationValue.containsKey('hours')) {
          hours = (durationValue['hours'] as num).toInt();
        }
        if (durationValue.containsKey('minutes')) {
          minutes = (durationValue['minutes'] as num).toInt();
        }
        if (durationValue.containsKey('seconds')) {
          seconds = (durationValue['seconds'] as num).toInt();
        }
        if (durationValue.containsKey('milliseconds')) {
          milliseconds = (durationValue['milliseconds'] as num).toInt();
        }
      } else if (durationValue is String) {
        // Parse duration string in format "HH:MM:SS.mmm"
        final parts = durationValue.split(':');
        if (parts.isNotEmpty) {
          hours = int.tryParse(parts[0]) ?? 0;
        }
        if (parts.length >= 2) {
          minutes = int.tryParse(parts[1]) ?? 0;
        }
        if (parts.length >= 3) {
          final secondsParts = parts[2].split('.');
          seconds = int.tryParse(secondsParts[0]) ?? 0;
          if (secondsParts.length > 1) {
            milliseconds = int.tryParse(secondsParts[1]) ?? 0;
          }
        }
      } else if (durationValue is int) {
        // Assume milliseconds
        return Duration(milliseconds: durationValue);
      }

      return Duration(
        hours: hours,
        minutes: minutes,
        seconds: seconds,
        milliseconds: milliseconds,
      );
    }

    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse text alignment
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.center;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'center':
            return TextAlign.center;
          case 'end':
          case 'right':
            return TextAlign.end;
          case 'start':
          case 'left':
            return TextAlign.start;
          case 'justify':
            return TextAlign.justify;
          default:
            return TextAlign.center;
        }
      }

      return TextAlign.center;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is bool && weightValue) {
        return FontWeight.bold;
      }

      return FontWeight.normal;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0.0);
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0.0);
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChanged'] = json['onChanged'];
      useJsonCallbacks = true;
    }

    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Create the widget with all properties from JSON
    return DurationWidget(
      initialValue: parseDuration(json['initialValue']),
      minDuration:
          json['minDuration'] != null
              ? parseDuration(json['minDuration'])
              : null,
      maxDuration:
          json['maxDuration'] != null
              ? parseDuration(json['maxDuration'])
              : null,
      showHours: json['showHours'] as bool? ?? true,
      showMinutes: json['showMinutes'] as bool? ?? true,
      showSeconds: json['showSeconds'] as bool? ?? true,
      showMilliseconds: json['showMilliseconds'] as bool? ?? false,
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']) ?? Color(0xFFCCCCCC),
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : 16.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      isCompact: json['isCompact'] as bool? ?? false,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      textAlign: parseTextAlign(json['textAlign']),
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      hoursLabel: json['hoursLabel'] as String? ?? 'h',
      minutesLabel: json['minutesLabel'] as String? ?? 'm',
      secondsLabel: json['secondsLabel'] as String? ?? 's',
      millisecondsLabel: json['millisecondsLabel'] as String? ?? 'ms',
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      autofocus: json['autofocus'] as bool? ?? false,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      width:
          json['width'] != null
              ? (json['width'] as num).toDouble()
              : double.infinity,
      height: json['height'] != null ? (json['height'] as num).toDouble() : 0,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      isVertical: json['isVertical'] as bool? ?? false,
      showSeparators: json['showSeparators'] as bool? ?? true,
      separator: json['separator'] as String? ?? ':',
      // Advanced interaction properties
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
    );
  }

  @override
  State<DurationWidget> createState() => _DurationWidgetState();
}

class _DurationWidgetState extends State<DurationWidget>
    with SingleTickerProviderStateMixin {
  late int _hours;
  late int _minutes;
  late int _seconds;
  late int _milliseconds;

  late TextEditingController _hoursController;
  late TextEditingController _minutesController;
  late TextEditingController _secondsController;
  late TextEditingController _millisecondsController;

  late FocusNode _hoursFocusNode;
  late FocusNode _minutesFocusNode;
  late FocusNode _secondsFocusNode;
  late FocusNode _millisecondsFocusNode;

  late AnimationController _animationController;
  late Animation<double> _animation;

  String? _errorText;

  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // Flag to track validation state
  bool _isValid = true;
  bool isHovered = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    // Initialize duration values
    _hours = widget.initialValue.inHours;
    _minutes = widget.initialValue.inMinutes.remainder(60);
    _seconds = widget.initialValue.inSeconds.remainder(60);
    _milliseconds = widget.initialValue.inMilliseconds.remainder(1000);

    // Initialize controllers with empty text to show placeholders
    _hoursController = TextEditingController(text: '');
    _minutesController = TextEditingController(text: '');
    _secondsController = TextEditingController(text: '');
    _millisecondsController = TextEditingController(text: '');

    // Initialize focus nodes
    _hoursFocusNode = widget.focusNode ?? FocusNode();
    _minutesFocusNode = FocusNode();
    _secondsFocusNode = FocusNode();
    _millisecondsFocusNode = FocusNode();
    focusNode = FocusNode();
    focusNode.addListener(() {
      setState(() {}); // Rebuild on focus change
    });

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Create animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Start animation if needed
    if (widget.hasAnimation) {
      _animationController.forward();
    }

    // Set initial error text
    _errorText = widget.errorText;

    // Validate initial value
    _validateDuration();

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      final currentDuration = Duration(
        hours: _hours,
        minutes: _minutes,
        seconds: _seconds,
        milliseconds: _milliseconds,
      );

      _callbackState['currentDuration'] = {
        'hours': _hours,
        'minutes': _minutes,
        'seconds': _seconds,
        'milliseconds': _milliseconds,
        'totalMilliseconds': currentDuration.inMilliseconds,
      };

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = currentDuration.inMilliseconds;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the current duration
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    final currentDuration = Duration(
      hours: _hours,
      minutes: _minutes,
      seconds: _seconds,
      milliseconds: _milliseconds,
    );

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply min duration validation
        if (rules.containsKey('minDuration')) {
          final minDuration = _parseDurationFromJson(rules['minDuration']);
          if (currentDuration < minDuration) {
            _isValid = false;
            _errorText =
                'Duration must be at least ${_formatDuration(minDuration)}';
            return;
          }
        }

        // Apply max duration validation
        if (rules.containsKey('maxDuration')) {
          final maxDuration = _parseDurationFromJson(rules['maxDuration']);
          if (currentDuration > maxDuration) {
            _isValid = false;
            _errorText =
                'Duration must be at most ${_formatDuration(maxDuration)}';
            return;
          }
        }

        // Apply custom validation
        if (rules.containsKey('custom') && rules['custom'] is String) {
          final customRule = rules['custom'] as String;

          // Example: Check if duration is even in seconds
          if (customRule == 'evenSeconds' &&
              currentDuration.inSeconds % 2 != 0) {
            _isValid = false;
            _errorText = 'Duration must be even in seconds';
            return;
          }

          // Example: Check if duration is odd in seconds
          if (customRule == 'oddSeconds' &&
              currentDuration.inSeconds % 2 == 0) {
            _isValid = false;
            _errorText = 'Duration must be odd in seconds';
            return;
          }
        }
      }
    }

    _isValid = true;
    _errorText = widget.errorText;
  }

  /// Applies JSON styling to the widget
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current duration
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  /// Parses a duration from a JSON value
  Duration _parseDurationFromJson(dynamic durationValue) {
    if (durationValue == null) return Duration.zero;

    int hours = 0;
    int minutes = 0;
    int seconds = 0;
    int milliseconds = 0;

    if (durationValue is Map) {
      if (durationValue.containsKey('hours')) {
        hours = (durationValue['hours'] as num).toInt();
      }
      if (durationValue.containsKey('minutes')) {
        minutes = (durationValue['minutes'] as num).toInt();
      }
      if (durationValue.containsKey('seconds')) {
        seconds = (durationValue['seconds'] as num).toInt();
      }
      if (durationValue.containsKey('milliseconds')) {
        milliseconds = (durationValue['milliseconds'] as num).toInt();
      }
    } else if (durationValue is String) {
      // Parse duration string in format "HH:MM:SS.mmm"
      final parts = durationValue.split(':');
      if (parts.isNotEmpty) {
        hours = int.tryParse(parts[0]) ?? 0;
      }
      if (parts.length >= 2) {
        minutes = int.tryParse(parts[1]) ?? 0;
      }
      if (parts.length >= 3) {
        final secondsParts = parts[2].split('.');
        seconds = int.tryParse(secondsParts[0]) ?? 0;
        if (secondsParts.length > 1) {
          milliseconds = int.tryParse(secondsParts[1]) ?? 0;
        }
      }
    } else if (durationValue is int) {
      // Assume milliseconds
      return Duration(milliseconds: durationValue);
    }

    return Duration(
      hours: hours,
      minutes: minutes,
      seconds: seconds,
      milliseconds: milliseconds,
    );
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

  @override
  void dispose() {
    _hoursController.dispose();
    _minutesController.dispose();
    _secondsController.dispose();
    _millisecondsController.dispose();

    _hoursFocusNode.dispose();
    _minutesFocusNode.dispose();
    _secondsFocusNode.dispose();
    _millisecondsFocusNode.dispose();

    _animationController.dispose();
    focusNode.dispose();

    super.dispose();
  }

  void _validateDuration() {
    final currentDuration = Duration(
      hours: _hours,
      minutes: _minutes,
      seconds: _seconds,
      milliseconds: _milliseconds,
    );

    if (widget.minDuration != null && currentDuration < widget.minDuration!) {
      setState(() {
        _errorText =
            'Duration must be at least ${_formatDuration(widget.minDuration!)}';
      });
    } else if (widget.maxDuration != null &&
        currentDuration > widget.maxDuration!) {
      setState(() {
        _errorText =
            'Duration must be at most ${_formatDuration(widget.maxDuration!)}';
      });
    } else {
      setState(() {
        _errorText = widget.errorText;
      });
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours h ${minutes > 0 ? '$minutes m' : ''}';
    } else if (minutes > 0) {
      return '$minutes m ${seconds > 0 ? '$seconds s' : ''}';
    } else {
      return seconds > 0 ? '$seconds s' : '0 s';
    }
  }

  void _updateDuration() {
    // Execute onBeforeChange callback if defined in JSON
    _executeJsonCallback('onBeforeChange');

    final currentDuration = Duration(
      hours: _hours,
      minutes: _minutes,
      seconds: _seconds,
      milliseconds: _milliseconds,
    );

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _applyJsonValidation();
    } else {
      _validateDuration();
    }

    // Call standard callback
    if (widget.onChanged != null) {
      widget.onChanged!(currentDuration);
    }

    // Execute JSON callback
    _executeJsonCallback('onChanged', currentDuration.inMilliseconds);

    if (widget.hasAnimation) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  Widget _buildTimeField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required int maxValue,
    required Function(String) onChanged,
    required bool isEnabled,
  }) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;
    final effectiveBorderColor =
        widget.isDarkTheme ? Colors.grey.shade600 : widget.borderColor;

    // Determine placeholder text based on label
    String placeholderText;
    switch (label.toLowerCase()) {
      case 'h':
        placeholderText = 'HH';
        break;
      case 'm':
        placeholderText = 'MM';
        break;
      case 's':
        placeholderText = 'SS';
        break;
      case 'ms':
        placeholderText = 'MS';
        break;
      default:
        placeholderText = 'DD';
    }

    return HoverableNumberInput(
      isCompact: widget.isCompact,
      hasBorder: widget.hasBorder,
      hasShadow: widget.hasShadow,
      borderRadius: widget.borderRadius,
      borderWidth: widget.borderWidth,
      fontSize: widget.fontSize,
      fontWeight: widget.fontWeight,
      isReadOnly: widget.isReadOnly,
      autofocus: widget.autofocus,
      isEnabled: isEnabled,
      placeholderText: placeholderText,
      maxValue: maxValue,
      onChanged: onChanged,
      onHover: widget.onHover,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;

    // Determine which fields to show
    final List<Widget> timeFields = [];

    if (widget.showHours) {
      timeFields.add(
        _buildTimeField(
          controller: _hoursController,
          focusNode: _hoursFocusNode,
          label: widget.hoursLabel ?? 'h',
          maxValue: 99,
          isEnabled: !widget.isDisabled,
          onChanged: (value) {
            _hours = int.tryParse(value) ?? 0;
            _updateDuration();
          },
        ),
      );
    }

    if (widget.showMinutes) {
      if (timeFields.isNotEmpty) {
        timeFields.add(
          const SizedBox(width: 8.0),
        ); // Add spacing instead of separator
      }

      timeFields.add(
        _buildTimeField(
          controller: _minutesController,
          focusNode: _minutesFocusNode,
          label: widget.minutesLabel ?? 'm',
          maxValue: 59,
          isEnabled: !widget.isDisabled,
          onChanged: (value) {
            _minutes = int.tryParse(value) ?? 0;
            if (_minutes > 59) {
              _minutes = 59;
              _minutesController.text = '59';
            }
            _updateDuration();
          },
        ),
      );
    }

    if (widget.showSeconds) {
      if (timeFields.isNotEmpty) {
        timeFields.add(
          const SizedBox(width: 8.0),
        ); // Add spacing instead of separator
      }

      timeFields.add(
        _buildTimeField(
          controller: _secondsController,
          focusNode: _secondsFocusNode,
          label: widget.secondsLabel ?? 's',
          maxValue: 59,
          isEnabled: !widget.isDisabled,
          onChanged: (value) {
            _seconds = int.tryParse(value) ?? 0;
            if (_seconds > 59) {
              _seconds = 59;
              _secondsController.text = '59';
            }
            _updateDuration();
          },
        ),
      );
    }

    if (widget.showMilliseconds) {
      if (timeFields.isNotEmpty) {
        timeFields.add(
          const SizedBox(width: 0.0),
        ); // Add spacing instead of separator
      }

      timeFields.add(
        _buildTimeField(
          controller: _millisecondsController,
          focusNode: _millisecondsFocusNode,
          label: widget.millisecondsLabel ?? 'ms',
          maxValue: 999,
          isEnabled: !widget.isDisabled,
          onChanged: (value) {
            _milliseconds = int.tryParse(value) ?? 0;
            if (_milliseconds > 999) {
              _milliseconds = 999;
              _millisecondsController.text = '999';
            }
            _updateDuration();
          },
        ),
      );
    }

    // Create the main layout
    Widget mainLayout;

    if (widget.isVertical) {
      mainLayout = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: timeFields,
      );
    } else {
      mainLayout = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: timeFields,
      );
    }

    // Apply animation if needed
    final animatedWidget =
        widget.hasAnimation
            ? FadeTransition(opacity: _animation, child: mainLayout)
            : mainLayout;

    // Apply shadow if needed
    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: animatedWidget,
            )
            : animatedWidget;

    // Create the final widget with label and error text
    return Container(
      width: widget.width,
      height: widget.height > 0 ? widget.height : null,
      margin: widget.margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.label != null) ...[
            Text(
              widget.label!,
              // style: TextStyle(
              //   fontSize: _getResponsiveFontSize(context),
              //   fontWeight: FontWeight.w500,
              //   fontFamily: 'Inter',
              //   color: widget.isDarkTheme ? Colors.white : Colors.black87,
              // ),
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: widget.isDarkTheme ? Colors.white : Colors.black87,
                fontSize: _getResponsiveFontSize(context),
              ),
            ),
            SizedBox(height: _getResponsiveBoxsize(context)),
          ],

          Align(alignment: Alignment.centerLeft, child: shadowWidget),

          if (_errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorText!,
              // style: TextStyle(
              //   color: Colors.red,
              //   fontSize: widget.fontSize * 0.8,
              // ),
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: Colors.red,
                fontSize: _getResponsiveIconSize(context),
              ),
            ),
          ] else if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              // style: TextStyle(
              //   color: effectiveTextColor.withAlpha(179),
              //   fontSize: widget.fontSize * 0.8,
              // ),
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: effectiveTextColor.withAlpha(179),
                fontSize: _getResponsiveIconSize(context),
              ),
            ),
          ],
        ],
      ),
    );
  }

  double _getResponsiveBoxsize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 8.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 8.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 6.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 4.0; // Small (768-1024px)
    } else {
      return 4.0; // Default for very small screens
    }
  }
}

class HoverableNumberInput extends StatefulWidget {
  final bool isCompact;
  final bool hasBorder;
  final bool hasShadow;
  final double borderRadius;
  final double borderWidth;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isReadOnly;
  final bool autofocus;
  final bool isEnabled;
  final String placeholderText;
  final int maxValue;
  final Function(String)? onChanged;
  final void Function(bool)? onHover;

  const HoverableNumberInput({
    super.key,
    required this.isCompact,
    required this.hasBorder,
    required this.hasShadow,
    required this.borderRadius,
    required this.borderWidth,
    required this.fontSize,
    required this.fontWeight,
    required this.isReadOnly,
    required this.autofocus,
    required this.isEnabled,
    required this.placeholderText,
    required this.maxValue,
    required this.onChanged,
    this.onHover,
  });

  @override
  State<HoverableNumberInput> createState() => _HoverableNumberInputState();
}

class _HoverableNumberInputState extends State<HoverableNumberInput> {
  bool isHovered = false;
  late FocusNode focusNode;
  final TextEditingController controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    focusNode = FocusNode();
    focusNode.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    focusNode.dispose();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveBorderColor = const Color(0xFFCCCCCC);
    final Color effectiveBackgroundColor = Colors.white;
    final Color effectiveTextColor = Colors.black;
    final bool isEnabled = widget.isEnabled;

    final borderColor =
        (focusNode.hasFocus || isHovered)
            ? const Color(0xFF0058FF)
            : effectiveBorderColor;

    final borderWidth =
        (focusNode.hasFocus || isHovered) ? 1.0 : widget.borderWidth;

    final containerHeight = _getResponsiveHeight(context);

    return SizedBox(
      //width: MediaQuery.of(context).size.width * (widget.isCompact ? 0.2 : 0.2),
      //width: MediaQuery.of(context).size.width * (5 / 100),
      //width: MediaQuery.of(context).size.width * (widget.isCompact ? 0.1 : 0.12),
      width: _getDurationColumnWidth(context),
      child: MouseRegion(
        onEnter: (_) {
          setState(() => isHovered = true);
          if (widget.onHover != null) widget.onHover!(true);
        },
        onExit: (_) {
          setState(() => isHovered = false);
          if (widget.onHover != null) widget.onHover!(false);
        },
        child: Container(
          height: containerHeight,
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border:
                widget.hasBorder
                    ? Border.all(color: borderColor, width: borderWidth)
                    : null,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  focusNode: focusNode,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  textAlignVertical: TextAlignVertical.top,
                  // style: TextStyle(
                  //   color: effectiveTextColor,
                  //   fontSize: _getResponsiveIconSize(context),
                  //   fontWeight: widget.fontWeight,
                  //   height: 1.0,
                  // ),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: effectiveTextColor,
                    fontSize: _getResponsiveIconSize(context),
                    height: 1,
                  ),
                  decoration: InputDecoration(
                    hintText: widget.placeholderText,
                    // hintStyle: TextStyle(
                    //   color: Colors.grey.shade500,
                    //   fontSize: _getResponsiveIconSize(context),
                    //   height: 1.0,
                    // ),
                    hintStyle: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: effectiveTextColor.withOpacity(0.6),
                      fontSize: _getResponsiveIconSize(context),
                      height: 1,
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    filled: false,
                    contentPadding: EdgeInsets.zero,
                    //contentPadding: _getResponsivePadding(context),
                    isDense: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(
                      widget.maxValue.toString().length,
                    ),
                  ],
                  enabled: isEnabled,
                  readOnly: widget.isReadOnly,
                  autofocus: widget.autofocus,
                  onChanged: widget.onChanged,
                  onTap: () => setState(() {}),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap:
                            isEnabled && !widget.isReadOnly
                                ? () {
                                  int currentValue =
                                      int.tryParse(controller.text) ?? 0;
                                  if (currentValue < widget.maxValue) {
                                    currentValue++;
                                    controller.text = currentValue.toString();
                                    widget.onChanged?.call(
                                      currentValue.toString(),
                                    );
                                  }
                                }
                                : null,
                        child: Container(
                          decoration: BoxDecoration(
                            color: effectiveBackgroundColor,
                            borderRadius: const BorderRadius.only(
                              topRight: Radius.circular(4),
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.keyboard_arrow_up,
                              size: 16,
                              color:
                                  isEnabled && !widget.isReadOnly
                                      ? Colors.grey.shade600
                                      : Colors.grey.shade400,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Container(height: 1, color: Colors.grey.shade300),
                    Expanded(
                      child: GestureDetector(
                        onTap:
                            isEnabled && !widget.isReadOnly
                                ? () {
                                  int currentValue =
                                      int.tryParse(controller.text) ?? 0;
                                  if (currentValue > 0) {
                                    currentValue--;
                                    controller.text = currentValue.toString();
                                    widget.onChanged?.call(
                                      currentValue.toString(),
                                    );
                                  }
                                }
                                : null,
                        child: Container(
                          decoration: BoxDecoration(
                            color: effectiveBackgroundColor,
                            borderRadius: const BorderRadius.only(
                              bottomRight: Radius.circular(4),
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              size: 16,
                              color:
                                  isEnabled && !widget.isReadOnly
                                      ? Colors.grey.shade600
                                      : Colors.grey.shade400,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else if (screenWidth >= 768) {
    return 14.0; // Small
  } else {
    return 14.0; // Extra Small (fallback for very small screens)
  }
}

double _getDurationColumnWidth(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth >= 1200) {
    return 75; // Desktop - 25%
  } else if (screenWidth >= 991) {
    return 55; // Tablet landscape - 33%
  } else if (screenWidth >= 768) {
    return 38; // Tablet portrait - 50%
  } else {
    return 55; // Mobile - full width
  }
}
