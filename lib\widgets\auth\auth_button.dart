import 'package:flutter/material.dart';
import 'package:nsl/theme/app_colors.dart';
import '../../ui_components/theme/app_theme.dart';

/// A reusable authentication button with loading state
class AuthButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;

  const AuthButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, 54),
        // padding: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingXs),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        // backgroundColor: Theme.of(context).colorScheme.primary,
        // foregroundColor: Theme.of(context).colorScheme.onPrimary,
        backgroundColor: AppColors.textBlue2,
        foregroundColor: AppColors.white,
        disabledBackgroundColor:
            Theme.of(context).colorScheme.primary.withAlpha(153),
      ),
      child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : Text(text),
    );
  }
}
