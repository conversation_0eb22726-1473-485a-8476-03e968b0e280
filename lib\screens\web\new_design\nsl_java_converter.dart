// import 'dart:convert'; // For JSON decoding - now handled by model class
import 'package:flutter/material.dart'; // Includes debugPrint
import 'package:flutter/services.dart'; // For rootBundle
import 'package:flutter/gestures.dart'; // For PointerDeviceKind
import '../../../models/nsl_java_converter_models.dart' as models;

class NslJavaConverter extends StatelessWidget {
  const NslJavaConverter({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'NSL Prescriptives to Java Code Highlighter',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const SentenceCodeHighlighter(),
    );
  }
}

class SentenceCodeHighlighter extends StatefulWidget {
  const SentenceCodeHighlighter({super.key});

  @override
  State<SentenceCodeHighlighter> createState() =>
      SentenceCodeHighlighterState();
}

class SentenceCodeHighlighterState extends State<SentenceCodeHighlighter> {
  int? selectedSentenceId;
  List<models.Code> code = [];
  List<models.Step> steps = [];
  List<models.StackModel> stacks = [];

  // File names and metrics from JSON
  String javaFileName = '';
  String nslFileName = '';
  int prescriptives = 0;
  int subPrescriptives = 0;
  int bets = 0;
  int pathways = 0;

  // Theme mode toggle
  bool _isDarkMode = false;
  bool showJavaKeywords = false;

  // Set to track keywords used in the code
  Set<String> _usedKeywords = {};

  // Currently selected keyword for highlighting
  String? _selectedKeyword;

  // Map to store line numbers where each keyword appears
  Map<String, List<int>> _keywordLineMap = {};

  // Key for the Java keywords button
  final GlobalKey _javaKeywordsButtonKey = GlobalKey();

  // Java keywords by category
  final Map<String, List<String>> _javaKeywords = {
    "Access Modifiers": ["public", "private", "protected"],
    "Class Related": [
      "class",
      "interface",
      "enum",
      "extends",
      "implements",
      "abstract",
      "final",
      "static",
      "builder"
    ],
    "Primitive Types": ["int", "boolean", "void"],
    "Reference Types": [
      "String",
      "Integer",
      "Boolean",
      "LocalDate",
      "List",
      "Role",
      "Map",
      "Optional",
      "PreparedStatement",
      "ResultSet",
      "Connection",
      "SQLException",
      "ValidationException",
      "LeaveApplication",
      "LeaveType",
      "LeaveStatus",
      "LeaveSubType",
      "User",
      "UserStatus",
      "ChronoUnit",
      "DateTimeFormatter",
      "UUID"
    ],
    "Control Flow": [
      "if",
      "else",
      "for",
      "while",
      "break",
      "continue",
      "return"
    ],
    "Exception Handling": ["try", "catch", "throw", "throws"],
    "Modifiers": ["static", "final", "abstract", "synchronized", "readonly"],
    "Object Related": ["this", "super", "new", "instanceof"],
    "Other Keywords": ["package", "import", "null", "true", "false"],
    "Operators": [
      "=",
      "+",
      "-",
      "*",
      "/",
      "==",
      "!=",
      ">",
      "<",
      ">=",
      "<=",
      "&&",
      "||",
      "!",
      "?:"
    ],
    "Syntax Elements": [
      "{",
      "}",
      "(",
      ")",
      "[",
      "]",
      ";",
      ".",
      ",",
      ":",
      "?",
      "\"",
      "@Override"
    ],
    "Common Methods": [
      "get",
      "set",
      "add",
      "remove",
      "isEmpty",
      "toString",
      "equals",
      "hashCode",
      "format",
      "parse",
      "trim",
      "length",
      "valueOf",
      "save",
      "findById",
      "validate",
      "build",
      "between",
      "now",
      "ofPattern"
    ],
    "Entity Specific Methods": [
      "getLeaveId",
      "setLeaveId",
      "getEmployeeId",
      "setEmployeeId",
      "getStartDate",
      "setStartDate",
      "getEndDate",
      "setEndDate",
      "getNumDays",
      "setNumDays",
      "getReason",
      "setReason",
      "getStatus",
      "setStatus",
      "getRemarks",
      "setRemarks",
      "getApprovedBy",
      "setApprovedBy",
      "getLeaveType",
      "setLeaveType",
      "getLeaveSubType",
      "setLeaveSubType",
      "getUserId",
      "setUserId",
      "getUsername",
      "setUsername",
      "getEmail",
      "setEmail",
      "getFirstName",
      "setFirstName",
      "getLastName",
      "setLastName",
      "getPasswordHash",
      "setPasswordHash",
      "getDisabled",
      "setDisabled",
      "getOrganization",
      "setOrganization",
      "getTeam",
      "setTeam",
      "getRoles",
      "setRoles",
      "getRoleId",
      "setRoleId",
      "getName",
      "setName",
      "getDescription",
      "setDescription",
      "getInheritsFrom",
      "setInheritsFrom",
      "getTenantId",
      "setTenantId",
      "getSubTypeId",
      "setSubTypeId",
      "getSubTypeName",
      "setSubTypeName",
      "getActive",
      "setActive"
    ],
    "Entity Fields": [
      "leaveId",
      "employeeId",
      "startDate",
      "endDate",
      "numDays",
      "reason",
      "status",
      "remarks",
      "approvedBy",
      "leaveType",
      "leaveSubType",
      "userId",
      "username",
      "email",
      "firstName",
      "lastName",
      "passwordHash",
      "disabled",
      "organization",
      "team",
      "roles",
      "roleId",
      "name",
      "description",
      "inheritsFrom",
      "tenantId",
      "subTypeId",
      "subTypeName",
      "active"
    ],
    "Enum Constants": [
      "PENDING",
      "APPROVED",
      "REJECTED",
      "ANNUAL_LEAVE",
      "SICK_LEAVE",
      "PARENTAL_LEAVE",
      "BEREAVEMENT",
      "ACTIVE",
      "INACTIVE",
      "SUSPENDED"
    ],
    "Database Keywords": [
      "PRIMARY KEY",
      "FOREIGN KEY",
      "VARCHAR",
      "INT",
      "TEXT",
      "DATE",
      "BOOLEAN",
      "NOT NULL",
      "UNIQUE",
      "REFERENCES",
      "WHERE",
      "AND",
      "OR",
      "SELECT",
      "INSERT",
      "UPDATE",
      "DELETE",
      "CREATE TABLE",
      "DEFAULT",
      "ON CONFLICT",
      "DO UPDATE SET"
    ],
    "UI Components": [
      "oj-input-text",
      "oj-input-date",
      "oj-input-number",
      "oj-text-area",
      "oj-text",
      "oj-combobox-one",
      "oj-select-single",
      "readonly"
    ],
    "Service Methods": [
      "applyForLeave",
      "managerApproval",
      "hrManagerApproval",
      "getLeaveApplicationById",
      "getLeaveSubTypesByLeaveType",
      "generateLeaveId",
      "calculateNumDays",
      "findByUsername",
      "findByEmployeeId",
      "findByStatus",
      "findByLeaveTypeAndActiveTrue",
      "loadUserRoles",
      "hasRole"
    ],
    "Workflow Related": [
      "workflow",
      "states",
      "transitions",
      "actions",
      "routing",
      "terminal",
      "complete",
      "Execute",
      "Read",
      "Update",
      "Create"
    ],
    "Validation Keywords": [
      "validate",
      "required",
      "unique",
      "exists",
      "conditional",
      "validation",
      "business rule",
      "derived",
      "dependent",
      "minimum",
      "maximum",
      "range",
      "format"
    ],
    "System Keywords": [
      "tenant",
      "scope",
      "permissions",
      "rights",
      "hierarchy",
      "audit",
      "timestamp",
      "default",
      "prefix",
      "ID generation",
      "authentication",
      "authorization"
    ]
  };

  // Theme constants
  static const Color _darkBackground = Color(0xFF2B2B2B);
  static const Color _darkHeaderColor = Color(0xFF3C3F41);
  static const Color _darkLineNumberBg = Color(0xFF313335);
  static const Color _darkBorderColor = Color(0xFF555555);
  static const Color _lightBackground = Colors.white;
  static const Color _lightHeaderColor = Color(0xFFBBDEFB); // Light blue
  static const Color _lightLineNumberBg = Color(0xFFEEEEEE);
  static const Color _lightBorderColor = Color(0xFFDDDDDD);

  // Scroll controller for the code panel
  late final ScrollController _codeScrollController;

  // Global key for the code panel to get accurate measurements
  final GlobalKey _codeViewKey = GlobalKey();

  // Map to store line positions for more accurate scrolling
  final Map<int, double> _linePositions = {};

  @override
  void initState() {
    super.initState();

    // Initialize the scroll controller with a listener
    _codeScrollController = ScrollController()
      ..addListener(() {
        // This helps improve scrolling accuracy after user interaction
        if (!_codeScrollController.position.isScrollingNotifier.value) {
          _updateLinePositions();
        }
      });

    _loadJsonData();

    // Add a post-frame callback to update line positions after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateLinePositions();
    });
  }

  @override
  void dispose() {
    _codeScrollController.dispose();
    super.dispose();
  }

  // Method to update line positions based on actual rendered positions
  void _updateLinePositions() {
    if (!_codeScrollController.hasClients) return;

    // We already have approximate positions from _buildCodeLines
    // This method could be extended to get more accurate positions
    // by measuring the actual rendered widgets if needed
  }

  // Method to scroll to a specific line in the code panel
  void _scrollToSelectedLine(int lineNumber) {
    // Ensure the controller is attached and the widget is built
    // Use microtask instead of post-frame callback for better performance
    Future.microtask(() {
      if (!_codeScrollController.hasClients) return;

      // Calculate position with some padding to center the line
      final double viewportHeight =
          _codeScrollController.position.viewportDimension;
      final double maxScroll = _codeScrollController.position.maxScrollExtent;

      // Get the position from our map or calculate it
      double targetPosition;

      if (_linePositions.containsKey(lineNumber)) {
        // Use the stored position if available
        targetPosition = _linePositions[lineNumber]!;
      } else {
        // Fallback to calculation if position not stored
        const double lineHeight = 24.0; // Base height per line
        targetPosition = (lineNumber - 1) * lineHeight;
      }

      // Add some padding to center the line in the viewport if possible
      final double padding =
          (viewportHeight / 2) - 12.0; // Half viewport minus half line height
      targetPosition = targetPosition - padding;

      // Ensure we don't scroll beyond bounds
      targetPosition = targetPosition.clamp(0.0, maxScroll);

      // Animate to the position with shorter duration for better responsiveness
      _codeScrollController.animateTo(
        targetPosition,
        duration: const Duration(milliseconds: 150), // Reduced from 300ms
        curve: Curves.easeInOut,
      );
    });
  }

  // Function to scan code and identify used keywords
  void _scanCodeForKeywords() {
    // Clear the set and map first
    _usedKeywords = {};
    _keywordLineMap = {};

    // Create a flat list of all keywords
    List<String> allKeywords = [];
    _javaKeywords.forEach((category, keywords) {
      allKeywords.addAll(keywords);
    });

    // Separate keywords into word-like and symbol-like categories
    List<String> wordKeywords = [];
    List<String> symbolKeywords = [];

    for (String keyword in allKeywords) {
      // Check if the keyword contains only word characters
      if (RegExp(r'^[\w]+$').hasMatch(keyword)) {
        wordKeywords.add(keyword);
      } else {
        // Escape special regex characters in symbols
        String escaped = keyword
            .replaceAll(r'\', r'\\')
            .replaceAll(r'.', r'\.')
            .replaceAll(r'(', r'\(')
            .replaceAll(r')', r'\)')
            .replaceAll(r'[', r'\[')
            .replaceAll(r']', r'\]')
            .replaceAll(r'{', r'\{')
            .replaceAll(r'}', r'\}')
            .replaceAll(r'+', r'\+')
            .replaceAll(r'*', r'\*')
            .replaceAll(r'?', r'\?')
            .replaceAll(r'^', r'\^')
            .replaceAll(r'$', r'\$')
            .replaceAll(r'|', r'\|');
        symbolKeywords.add(escaped);
      }
    }

    // Create regex patterns for both types of keywords
    RegExp? wordPattern;
    if (wordKeywords.isNotEmpty) {
      wordPattern = RegExp(
        r'\b(' + wordKeywords.join('|') + r')\b',
        caseSensitive: true,
      );
    }

    RegExp? symbolPattern;
    if (symbolKeywords.isNotEmpty) {
      symbolPattern = RegExp(
        symbolKeywords.join('|'),
        caseSensitive: true,
      );
    }

    // Scan each line of code for keywords
    for (var line in code) {
      String lineText = line.code ?? '';
      int lineNumber = line.line ?? 0;

      // Process word-like keywords
      if (wordPattern != null) {
        for (Match match in wordPattern.allMatches(lineText)) {
          String? keyword = match.group(0);
          if (keyword != null) {
            // Add to used keywords set
            _usedKeywords.add(keyword);

            // Add to line map
            if (!_keywordLineMap.containsKey(keyword)) {
              _keywordLineMap[keyword] = [];
            }
            if (!_keywordLineMap[keyword]!.contains(lineNumber)) {
              _keywordLineMap[keyword]!.add(lineNumber);
            }
          }
        }
      }

      // Process symbol-like keywords
      if (symbolPattern != null) {
        for (Match match in symbolPattern.allMatches(lineText)) {
          String? keyword = match.group(0);
          if (keyword != null) {
            // Add to used keywords set
            _usedKeywords.add(keyword);

            // Add to line map
            if (!_keywordLineMap.containsKey(keyword)) {
              _keywordLineMap[keyword] = [];
            }
            if (!_keywordLineMap[keyword]!.contains(lineNumber)) {
              _keywordLineMap[keyword]!.add(lineNumber);
            }
          }
        }
      }
    }

    // Debug output
    // debugPrint(
    //     'Found ${_usedKeywords.length} used keywords: ${_usedKeywords.join(', ')}');
    // _keywordLineMap.forEach((keyword, lines) {
    //   debugPrint('Keyword "$keyword" appears on lines: ${lines.join(', ')}');
    // });
  }

  // Function to scroll to and highlight all occurrences of a keyword
  void _highlightKeyword(String keyword) {
    if (!_usedKeywords.contains(keyword)) {
      // If keyword is not used, do nothing
      return;
    }

    print("Highlighting keyword: $keyword");

    setState(() {
      // Do NOT clear sentence selection when selecting a keyword
      // Keep the selectedSentenceId as is

      // If the same keyword is clicked again, deselect it
      if (_selectedKeyword == keyword) {
        _selectedKeyword = null;
        print("Deselected keyword: $keyword");
      } else {
        _selectedKeyword = keyword;
        print("Selected keyword: $_selectedKeyword");

        // Scroll to the first occurrence of the keyword
        if (_keywordLineMap.containsKey(keyword) &&
            _keywordLineMap[keyword]!.isNotEmpty) {
          print("Scrolling to line: ${_keywordLineMap[keyword]![0]}");
          _scrollToSelectedLine(_keywordLineMap[keyword]![0]);
        }
      }

      // Force a rebuild of the code view
      if (_codeViewKey.currentContext != null) {
        print("Forcing rebuild of code view");
      }
    });
  }

  // Function to highlight related keywords from a step
  void _highlightRelatedKeywords(List<String> keywords) {
    if (keywords.isEmpty) {
      // If no keywords, do nothing
      return;
    }

    // Don't automatically select any keyword
    // Just make sure the keywords are available in the UI
    // The user will select them manually if needed

    // DO NOT clear any previously selected keyword
    // This allows the user to keep a keyword selected when clicking on a step
    print("Related keywords: $keywords");
    print("Current selected keyword: $_selectedKeyword");
  }

  // Function to load JSON data from the file
  Future<void> _loadJsonData() async {
    try {
      // Load the JSON file
      String jsonString = await rootBundle
          .loadString('assets/data/new_leave_application_data_1.json');

      // Use our model class to parse the JSON data
      models.NslJavaConverterData data =
          models.nslJavaConverterDataFromJson(jsonString);

      // Extract all steps from stacks for easy access
      List<models.Step> allSteps = [];
      for (var stack in data.stacks ?? []) {
        if (stack.steps != null) {
          allSteps.addAll(stack.steps!);

          // Also add inner components if they exist
          for (var step in stack.steps!) {
            if (step.innnerComponents != null) {
              allSteps.addAll(step.innnerComponents!);
            }
          }
        }
      }

      setState(() {
        code = data.code ?? [];
        stacks = data.stacks ?? [];
        steps = allSteps;
        javaFileName = data.javaFileName ?? 'LeaveApplication.java';
        nslFileName = data.nslFileName ?? 'LeaveApplication.nsl';
        prescriptives = data.prescriptives ?? 0;
        subPrescriptives = data.subPrescriptives ?? 0;
        bets = data.beTs ?? 0;
        pathways = data.pathways ?? 0;
      });

      // Scan the code for used keywords after loading
      _scanCodeForKeywords();
    } catch (e, stackTrace) {
      // Log error to console
      debugPrint(
        'Error loading JSON data: $e $stackTrace',
      );
      // Initialize with empty lists and default values to prevent null errors
      setState(() {
        code = [];
        steps = [];
        stacks = [];
        _usedKeywords = {};
        javaFileName = 'LeaveApplication.java';
        nslFileName = 'LeaveApplication.nsl';
      });
    }
  }

  // Function to get highlighted code lines with Java syntax highlighting
  // Groups continuous lines together in a single highlight box
  List<Widget> _buildCodeLines(int? selectedSentenceId) {
    if (code.isEmpty) return [];

    // If no sentence is selected, just render all lines normally
    if (selectedSentenceId == null) {
      return _buildRegularCodeLines();
    }

    // Find the selected step and its related lines
    models.Step? selectedStep;

    // First, check if the selected sentence is in a regular step
    for (var step in steps) {
      if (step.sentenceId == selectedSentenceId) {
        selectedStep = step;
        break;
      }
    }

    // If not found in regular steps, check if it's in inner components
    if (selectedStep == null) {
      for (var stack in stacks) {
        if (stack.steps != null) {
          for (var step in stack.steps!) {
            // Check if this step has inner components
            if (step.innnerComponents != null) {
              for (var component in step.innnerComponents!) {
                if (component.sentenceId == selectedSentenceId) {
                  selectedStep = component;
                  break;
                }
              }
              if (selectedStep != null) break;
            }
          }
        }
        if (selectedStep != null) break;
      }
    }

    // If no step found, render normally
    if (selectedStep == null) {
      return _buildRegularCodeLines();
    }

    // Get the related lines and color for the selected step
    List<int> relatedLines = selectedStep.relatedLines ?? [];
    Color highlightColor =
        Color(int.parse('0xFF${selectedStep.color?.substring(1) ?? '000000'}'));

    // Get related keywords if they exist
    List<String> relatedKeywords = [];
    if (selectedStep.relatedKeywords != null &&
        selectedStep.relatedKeywords!.isNotEmpty) {
      // Extract keywords from the relatedKeywords list
      for (var keywordObj in selectedStep.relatedKeywords!) {
        if (keywordObj.keyword != null) {
          relatedKeywords.add(keywordObj.keyword!);
        }
      }

      // Make the related keywords available in the UI
      // but don't automatically select any of them
      if (relatedKeywords.isNotEmpty) {
        _highlightRelatedKeywords(relatedKeywords);
      }

      // Print the selected keyword for debugging
      print("Selected keyword in _buildCodeLines: $_selectedKeyword");
    }

    // Sort the related lines to ensure they're in order
    relatedLines.sort();

    // Group continuous lines
    List<List<int>> lineGroups = [];
    if (relatedLines.isNotEmpty) {
      List<int> currentGroup = [relatedLines[0]];

      for (int i = 1; i < relatedLines.length; i++) {
        // If this line is consecutive with the previous one, add to current group
        if (relatedLines[i] == relatedLines[i - 1] + 1) {
          currentGroup.add(relatedLines[i]);
        } else {
          // Otherwise, start a new group
          lineGroups.add(List<int>.from(currentGroup));
          currentGroup = [relatedLines[i]];
        }
      }

      // Add the last group
      lineGroups.add(List<int>.from(currentGroup));
    }

    // Build the widgets for each line
    List<Widget> widgets = [];

    // Process each line in the code
    for (int i = 0; i < code.length; i++) {
      var line = code[i];
      int lineNumber = line.line ?? 0;

      // Store position for scrolling
      _linePositions[lineNumber] = (lineNumber - 1) * 24.0;

      // Check if this line starts a group
      bool isGroupStart = false;
      List<int>? currentGroup;

      for (var group in lineGroups) {
        if (group.contains(lineNumber) && (group.first == lineNumber)) {
          isGroupStart = true;
          currentGroup = group;
          break;
        }
      }

      // If this is the start of a group, create a container for the whole group
      if (isGroupStart && currentGroup != null) {
        // Calculate how many lines are in this group
        int groupLength = currentGroup.length;

        // Create a list to hold all the code lines in this group
        List<Widget> groupCodeLines = [];

        // Add each line in the group
        for (int j = 0; j < groupLength; j++) {
          if (i + j < code.length && code[i + j].line == currentGroup[j]) {
            var groupLine = code[i + j];
            String lineText = groupLine.code ?? '';
            String lineNumberText = (groupLine.line ?? 0).toString().padLeft(3);

            // Create the line number widget
            Widget lineNumberWidget = Container(
              width: 50,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: lineNumberBackground, // Dynamic line number background
                border: Border(
                  right: BorderSide(
                    color: borderColor,
                    width: 1,
                  ),
                ),
              ),
              child: Text(
                lineNumberText,
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                  color: lineNumberTextColor,
                ),
                textAlign: TextAlign.right,
              ),
            );

            // Create the code text widget with syntax highlighting
            Widget codeTextWidget = RichText(
              text: TextSpan(
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                  color: textColor,
                ),
                children: _applySyntaxHighlighting(lineText),
              ),
              softWrap: false,
              overflow: TextOverflow.visible,
            );

            // Add the row with line number and code
            groupCodeLines.add(
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 1.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize
                      .min, // Prevent overflow by using minimum size
                  children: [
                    // Line number
                    lineNumberWidget,
                    // Code content - allow it to take full width
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 2),
                      child: codeTextWidget,
                    ),
                    // No spacer needed as we're using mainAxisSize.min
                  ],
                ),
              ),
            );
          }
        }

        // Find the longest line in the group to determine the width of the highlight
        String longestLine = "";
        for (int j = 0; j < groupLength; j++) {
          if (i + j < code.length && code[i + j].line == currentGroup[j]) {
            String lineText = code[i + j].code ?? '';
            if (lineText.length > longestLine.length) {
              longestLine = lineText;
            }
          }
        }

        // Create a container for the whole group with a single highlight
        // Calculate width based on the content, but don't limit it for horizontal scrolling
        // double contentWidth = _calculateTextWidth(longestLine) + 24;

        // Get the ID from the selected step to display in the badge
        String stepId = selectedStep.id ?? '';

        widgets.add(
          Stack(
            clipBehavior: Clip.none,
            children: [
              // Main highlighted container
              ClipRect(
                // Clip any overflow
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  decoration: BoxDecoration(
                    color: highlightColor.withAlpha(77), // 0.3 * 255 = 77
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: highlightColor.withAlpha(100),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  // Allow the container to take as much width as needed for the content
                  // width: contentWidth,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: groupCodeLines,
                  ),
                ),
              ),
              // ID Badge at the top right corner
              Positioned(
                top: -10,
                right: 0,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: highlightColor
                        .withAlpha(200), // Higher opacity for better visibility
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: highlightColor, width: 1),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(50),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    stepId,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );

        // Skip the lines we've already processed
        i += groupLength - 1;
      } else if (relatedLines.contains(lineNumber)) {
        // This is a highlighted line (not part of a group)
        String lineText = line.code ?? '';
        String lineNumberText = lineNumber.toString().padLeft(3);

        // Create the line number widget
        Widget lineNumberWidget = Container(
          width: 50,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: lineNumberBackground, // Dynamic line number background
            border: Border(
              right: BorderSide(
                color: borderColor,
                width: 1,
              ),
            ),
          ),
          child: Text(
            lineNumberText,
            style: TextStyle(
              fontFamily: 'monospace',
              fontSize: 14,
              color: lineNumberTextColor,
            ),
            textAlign: TextAlign.right,
          ),
        );

        // Create the code text widget with syntax highlighting
        Widget codeTextWidget = RichText(
          text: TextSpan(
            style: TextStyle(
              fontFamily: 'monospace',
              fontSize: 14,
              color: textColor,
            ),
            children: _applySyntaxHighlighting(lineText),
          ),
          softWrap: false,
          overflow: TextOverflow.visible,
        );

        // Get the ID from the selected step to display in the badge
        String stepId = selectedStep.id ?? '';

        // Calculate width based on the content, but don't limit it for horizontal scrolling
        // double contentWidth = _calculateTextWidth(lineText) + 24;

        // Create the highlighted line widget with only the code content highlighted
        widgets.add(
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize:
                  MainAxisSize.min, // Prevent overflow by using minimum size
              children: [
                // Line number column
                lineNumberWidget,
                // Code content with highlight - width based on text content
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    ClipRect(
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 2),
                        decoration: BoxDecoration(
                          color: highlightColor.withAlpha(77), // 0.3 * 255 = 77
                          borderRadius: BorderRadius.circular(4),
                          boxShadow: [
                            BoxShadow(
                              color: highlightColor.withAlpha(100),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        // Allow the container to take as much width as needed for the content
                        // width: contentWidth,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 2),
                          child: codeTextWidget,
                        ),
                      ),
                    ),
                    // ID Badge at the top right corner
                    Positioned(
                      top: -10,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: highlightColor.withAlpha(
                              200), // Higher opacity for better visibility
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: highlightColor, width: 1),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(50),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Text(
                          stepId,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                // No spacer needed as we're using mainAxisSize.min
              ],
            ),
          ),
        );
      } else {
        // This is a regular non-highlighted line
        String lineText = line.code ?? '';
        String lineNumberText = lineNumber.toString().padLeft(3);

        // Create the line widget
        widgets.add(
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize:
                  MainAxisSize.min, // Prevent overflow by using minimum size
              children: [
                // Line number column
                Container(
                  width: 50,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color:
                        lineNumberBackground, // Dynamic line number background
                    border: Border(
                      right: BorderSide(
                        color: borderColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Text(
                    lineNumberText,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
                // Code content - allow it to take full width
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                  child: RichText(
                    text: TextSpan(
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                        color: textColor,
                      ),
                      children: _applySyntaxHighlighting(lineText),
                    ),
                    softWrap: false,
                    overflow: TextOverflow.visible,
                  ),
                ),
                // No spacer needed as we're using mainAxisSize.min
              ],
            ),
          ),
        );
      }
    }

    return widgets;
  }

  // Helper method to build regular code lines without grouping
  List<Widget> _buildRegularCodeLines() {
    return code.map((line) {
      // Get the code text with line number
      String lineText = line.code ?? '';
      String lineNumber = (line.line ?? 0).toString().padLeft(3);

      // Store position for scrolling
      _linePositions[line.line ?? 0] = (line.line ?? 0 - 1) * 24.0;

      // Apply Java syntax highlighting
      Widget codeText = RichText(
        text: TextSpan(
          style: TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            color: textColor,
          ),
          children: _applySyntaxHighlighting(lineText),
        ),
        softWrap: false,
        overflow: TextOverflow.visible,
      );

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 1.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize:
              MainAxisSize.min, // Prevent overflow by using minimum size
          children: [
            // Line number column
            Container(
              width: 50,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: lineNumberBackground, // Dynamic line number background
                border: Border(
                  right: BorderSide(
                    color: borderColor,
                    width: 1,
                  ),
                ),
              ),
              child: Text(
                lineNumber,
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                  color: lineNumberTextColor,
                ),
                textAlign: TextAlign.right,
              ),
            ),
            // Code content - only as wide as the text, wrapped in Flexible to prevent overflow
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
              child: codeText,
            ),
            // No spacer needed as we're using mainAxisSize.min
          ],
        ),
      );
    }).toList();
  }

  // Helper method to calculate the width of text
  // double _calculateTextWidth(String text) {
  //   // Create a TextPainter to measure the text width
  //   final TextPainter textPainter = TextPainter(
  //     text: TextSpan(
  //       text: text,
  //       style: const TextStyle(
  //         fontFamily: 'monospace',
  //         fontSize: 14,
  //       ),
  //     ),
  //     maxLines: 1,
  //     textDirection: TextDirection.ltr,
  //   )..layout();

  //   // Return the actual width without capping it
  //   // This allows for proper horizontal scrolling
  //   return textPainter.width;
  // }

  // Helper method to apply basic Java syntax highlighting
  List<TextSpan> _applySyntaxHighlighting(String code) {
    // Create a flat list of all keywords
    List<String> allKeywords = [];
    _javaKeywords.forEach((category, keywords) {
      // Skip operators and syntax elements for the syntax highlighting pattern
      if (category != 'Operators' && category != 'Syntax Elements') {
        allKeywords.addAll(keywords);
      }
    });

    // Define regex patterns for Java syntax
    final RegExp keywordPattern = RegExp(
      r'\b(' + allKeywords.join('|') + r')\b',
      caseSensitive: true,
    );

    // Note: We don't need to create operator and syntax element patterns here
    // as they are created and used in the _processCodeSegment method

    final RegExp stringPattern = RegExp(r'"[^"]*"');

    // Create a pattern for the selected keyword if one is selected
    RegExp? selectedKeywordPattern;
    if (_selectedKeyword != null) {
      print(
          "Applying syntax highlighting with selected keyword: $_selectedKeyword");

      // Check if the selected keyword is a regular word or a special character
      if (RegExp(r'^[\w]+$').hasMatch(_selectedKeyword!)) {
        selectedKeywordPattern = RegExp(
          r'\b(' + RegExp.escape(_selectedKeyword!) + r')\b',
          caseSensitive: true,
        );
      } else {
        selectedKeywordPattern = RegExp(
          RegExp.escape(_selectedKeyword!),
          caseSensitive: true,
        );
      }
    }

    // Colors for different syntax elements
    const Color keywordColor = Color(0xFFCC7832); // Orange
    const Color stringColor = Color(0xFF6A8759); // Green
    final Color selectedKeywordColor = Colors.red; // Red for selected keyword
    final Color selectedKeywordBgColor = Color.fromRGBO(
        255, 255, 0, 0.3); // Yellow background for selected keyword

    // Split the code into parts to apply different styles
    List<TextSpan> spans = [];

    // Process the code
    int lastIndex = 0;

    // First, check for the selected keyword if there is one
    if (selectedKeywordPattern != null) {
      // Check if the selected keyword is in this line of code
      if (selectedKeywordPattern.hasMatch(code)) {
        print("Found selected keyword in code: $_selectedKeyword");

        for (Match match in selectedKeywordPattern.allMatches(code)) {
          // Process text before the selected keyword
          if (match.start > lastIndex) {
            // This section might contain other keywords or strings, so process it recursively
            String beforeText = code.substring(lastIndex, match.start);
            spans.addAll(_processCodeSegment(beforeText, keywordPattern,
                stringPattern, keywordColor, stringColor));
          }

          // Add the highlighted selected keyword with special styling
          spans.add(TextSpan(
            text: match.group(0),
            style: TextStyle(
              color: selectedKeywordColor,
              fontWeight: FontWeight.bold,
              backgroundColor: selectedKeywordBgColor,
            ),
          ));

          lastIndex = match.end;
        }

        // Process any remaining text after the last selected keyword
        if (lastIndex < code.length) {
          String afterText = code.substring(lastIndex);
          spans.addAll(_processCodeSegment(afterText, keywordPattern,
              stringPattern, keywordColor, stringColor));
        }
      } else {
        // Selected keyword not found in this line, process normally
        spans.addAll(_processCodeSegment(
            code, keywordPattern, stringPattern, keywordColor, stringColor));
      }
    } else {
      // No selected keyword, just process normally
      spans.addAll(_processCodeSegment(
          code, keywordPattern, stringPattern, keywordColor, stringColor));
    }

    return spans;
  }

  // Helper method to process a segment of code for regular syntax highlighting
  List<TextSpan> _processCodeSegment(String code, RegExp keywordPattern,
      RegExp stringPattern, Color keywordColor, Color stringColor) {
    List<TextSpan> spans = [];

    // Create patterns for operators and syntax elements
    List<String> operators = _javaKeywords['Operators'] ?? [];
    List<String> syntaxElements = _javaKeywords['Syntax Elements'] ?? [];

    // Escape special regex characters
    List<String> escapedOperators =
        operators.map((op) => RegExp.escape(op)).toList();

    List<String> escapedSyntaxElements =
        syntaxElements.map((element) => RegExp.escape(element)).toList();

    // Create combined pattern for operators and syntax elements
    RegExp? operatorPattern;
    if (escapedOperators.isNotEmpty) {
      operatorPattern = RegExp(
        escapedOperators.join('|'),
        caseSensitive: true,
      );
    }

    RegExp? syntaxElementPattern;
    if (escapedSyntaxElements.isNotEmpty) {
      syntaxElementPattern = RegExp(
        escapedSyntaxElements.join('|'),
        caseSensitive: true,
      );
    }

    // Colors for operators and syntax elements
    const Color operatorColor = Color(0xFFCC7832); // Orange (same as keywords)
    const Color syntaxElementColor = Color(0xFF808080); // Grey

    // Check for keywords
    if (keywordPattern.hasMatch(code)) {
      int lastIndex = 0;

      for (Match match in keywordPattern.allMatches(code)) {
        // Add text before the keyword
        if (match.start > lastIndex) {
          String beforeText = code.substring(lastIndex, match.start);
          // Process operators and syntax elements in the text before the keyword
          _processOperatorsAndSyntax(beforeText, operatorPattern,
              syntaxElementPattern, operatorColor, syntaxElementColor, spans);
        }

        // Add the highlighted keyword
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(color: keywordColor, fontWeight: FontWeight.bold),
        ));

        lastIndex = match.end;
      }

      // Add any remaining text
      if (lastIndex < code.length) {
        String afterText = code.substring(lastIndex);
        // Process operators and syntax elements in the remaining text
        _processOperatorsAndSyntax(afterText, operatorPattern,
            syntaxElementPattern, operatorColor, syntaxElementColor, spans);
      }
    }
    // Check for strings
    else if (stringPattern.hasMatch(code)) {
      int lastIndex = 0;

      for (Match match in stringPattern.allMatches(code)) {
        // Add text before the string
        if (match.start > lastIndex) {
          String beforeText = code.substring(lastIndex, match.start);
          // Process operators and syntax elements in the text before the string
          _processOperatorsAndSyntax(beforeText, operatorPattern,
              syntaxElementPattern, operatorColor, syntaxElementColor, spans);
        }

        // Add the highlighted string
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(color: stringColor),
        ));

        lastIndex = match.end;
      }

      // Add any remaining text
      if (lastIndex < code.length) {
        String afterText = code.substring(lastIndex);
        // Process operators and syntax elements in the remaining text
        _processOperatorsAndSyntax(afterText, operatorPattern,
            syntaxElementPattern, operatorColor, syntaxElementColor, spans);
      }
    }
    // If no special patterns match, process operators and syntax elements
    else {
      _processOperatorsAndSyntax(code, operatorPattern, syntaxElementPattern,
          operatorColor, syntaxElementColor, spans);
    }

    return spans;
  }

  // Helper method to process operators and syntax elements
  void _processOperatorsAndSyntax(
      String code,
      RegExp? operatorPattern,
      RegExp? syntaxElementPattern,
      Color operatorColor,
      Color syntaxElementColor,
      List<TextSpan> spans) {
    // If no operator or syntax element patterns, just add the plain text
    if (operatorPattern == null && syntaxElementPattern == null) {
      spans.add(TextSpan(text: code));
      return;
    }

    // Process operators first
    if (operatorPattern != null && operatorPattern.hasMatch(code)) {
      int lastIndex = 0;

      for (Match match in operatorPattern.allMatches(code)) {
        // Add text before the operator
        if (match.start > lastIndex) {
          String beforeText = code.substring(lastIndex, match.start);
          // Process syntax elements in the text before the operator
          _processSyntaxElements(
              beforeText, syntaxElementPattern, syntaxElementColor, spans);
        }

        // Add the highlighted operator
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(color: operatorColor, fontWeight: FontWeight.bold),
        ));

        lastIndex = match.end;
      }

      // Add any remaining text
      if (lastIndex < code.length) {
        String afterText = code.substring(lastIndex);
        // Process syntax elements in the remaining text
        _processSyntaxElements(
            afterText, syntaxElementPattern, syntaxElementColor, spans);
      }
    }
    // If no operators, process syntax elements
    else {
      _processSyntaxElements(
          code, syntaxElementPattern, syntaxElementColor, spans);
    }
  }

  // Helper method to process syntax elements
  void _processSyntaxElements(String code, RegExp? syntaxElementPattern,
      Color syntaxElementColor, List<TextSpan> spans) {
    // If no syntax element pattern, just add the plain text
    if (syntaxElementPattern == null) {
      spans.add(TextSpan(text: code));
      return;
    }

    // Process syntax elements
    if (syntaxElementPattern.hasMatch(code)) {
      int lastIndex = 0;

      for (Match match in syntaxElementPattern.allMatches(code)) {
        // Add text before the syntax element
        if (match.start > lastIndex) {
          spans.add(TextSpan(text: code.substring(lastIndex, match.start)));
        }

        // Add the highlighted syntax element
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(color: syntaxElementColor),
        ));

        lastIndex = match.end;
      }

      // Add any remaining text
      if (lastIndex < code.length) {
        spans.add(TextSpan(text: code.substring(lastIndex)));
      }
    }
    // If no syntax elements match, just add the plain text
    else {
      spans.add(TextSpan(text: code));
    }
  }

  // Helper method to build RichText for inner components
  Widget _buildInnerComponentsText(models.Step step, bool isSelected) {
    // Get the inner components
    List<models.Step> innerComponents = step.innnerComponents ?? [];

    // Create a list of TextSpans for the RichText widget
    List<InlineSpan> spans = [];

    for (var component in innerComponents) {
      // Get the component properties
      String text = component.sentence ?? '';
      bool clickable = component.clickable ?? false;
      Color componentColor =
          Color(int.parse('0xFF${component.color?.substring(1) ?? '000000'}'));

      // Create a TextSpan for this component
      if (clickable) {
        // Check if this component is the selected one
        bool isComponentSelected = selectedSentenceId == component.sentenceId;

        spans.add(
          TextSpan(
            text: text,
            style: TextStyle(
              color: isComponentSelected
                  ? componentColor.withAlpha(220)
                  : Colors.black87,
              fontWeight:
                  isComponentSelected ? FontWeight.bold : FontWeight.normal,
              // decoration: TextDecoration.underline,
              // decorationColor: componentColor,
              // decorationThickness: 2.0,
              backgroundColor: isComponentSelected
                  ? componentColor.withAlpha(40)
                  : Colors.transparent,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                // Pre-fetch related lines before setState to improve performance
                final relatedLines = component.relatedLines;
                final firstLine =
                    relatedLines != null && relatedLines.isNotEmpty
                        ? relatedLines[0]
                        : null;

                setState(() {
                  // Do NOT clear keyword selection when selecting a component
                  // Keep the _selectedKeyword as is

                  // Set the selected sentence ID
                  selectedSentenceId = component.sentenceId;
                });

                // Scroll immediately without delay if we have a line to scroll to
                if (firstLine != null) {
                  _scrollToSelectedLine(firstLine);
                }
              },
          ),
        );
      } else {
        // Non-clickable component
        spans.add(
          TextSpan(
            text: text,
            style: TextStyle(
              color: isSelected ? Colors.black.withAlpha(220) : Colors.black87,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        );
      }

      // Add a space between components if this is not the last one
      if (component != innerComponents.last) {
        spans.add(const TextSpan(text: ' '));
      }
    }

    // Return the RichText widget
    return RichText(
      text: TextSpan(children: spans),
      softWrap: true,
    );
  }

  // Helper method to get the current theme colors
  Color get editorBackground =>
      _isDarkMode ? _darkBackground : _lightBackground;
  Color get editorHeaderColor =>
      _isDarkMode ? _darkHeaderColor : _lightHeaderColor;
  Color get lineNumberBackground =>
      _isDarkMode ? _darkLineNumberBg : _lightLineNumberBg;
  Color get borderColor => _isDarkMode ? _darkBorderColor : _lightBorderColor;
  Color get textColor => _isDarkMode ? Colors.white : Colors.black;
  Color get lineNumberTextColor =>
      _isDarkMode ? Colors.grey[500]! : Colors.grey[700]!;
  Color get headerIconColor => _isDarkMode ? Colors.white70 : Colors.blue[800]!;
  Color get headerTextColor => _isDarkMode ? Colors.white : Colors.blue[900]!;

  // Toggle theme mode
  void _toggleThemeMode() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  Widget javaKeywordsContainer() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          // Container(
          //   padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          //   decoration: BoxDecoration(
          //     color: _isDarkMode ? const Color(0xFF3C3F41) : Colors.blue[50],
          //     borderRadius: BorderRadius.circular(4),
          //   ),
          //   child: Row(
          //     children: [
          //       Icon(
          //         Icons.code,
          //         color: _isDarkMode ? Colors.white70 : Colors.blue[800],
          //         size: 16,
          //       ),
          //       const SizedBox(width: 8),
          //       Text(
          //         'Java Keywords',
          //         style: TextStyle(
          //           fontSize: 14,
          //           fontWeight: FontWeight.bold,
          //           color: _isDarkMode ? Colors.white : Colors.blue[900],
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // const SizedBox(height: 12),

          // If a sentence is selected, show tab view with related keywords and all keywords
          // Otherwise, show the regular keywords view
          Expanded(
            child: selectedSentenceId != null
                ? DefaultTabController(
                    length: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: _isDarkMode
                                ? const Color(0xFF3C3F41)
                                : Colors.blue[50],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: TabBar(
                            labelColor:
                                _isDarkMode ? Colors.white : Colors.blue[900],
                            unselectedLabelColor:
                                _isDarkMode ? Colors.white70 : Colors.blue[300],
                            indicatorColor: _isDarkMode
                                ? Colors.blue[400]
                                : Colors.blue[700],
                            indicatorSize: TabBarIndicatorSize.tab,
                            tabs: const [
                              Tab(text: 'Used Constructs'),
                              Tab(text: 'All Constructs'),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: TabBarView(
                            children: [
                              // Tab 1: Related Keywords for the selected sentence
                              _buildRelatedKeywordsTab(),

                              // Tab 2: All Keywords (current implementation)
                              _buildAllKeywordsTab(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : _buildAllKeywordsTab(),
          ),
        ],
      ),
    );
  }

  // Widget to build the related keywords tab
  Widget _buildRelatedKeywordsTab() {
    // Find the selected step to get related keywords
    models.Step? selectedStep;

    // First, check if the selected sentence is in a regular step
    for (var step in steps) {
      if (step.sentenceId == selectedSentenceId) {
        selectedStep = step;
        break;
      }
    }

    // If not found in regular steps, check if it's in inner components
    if (selectedStep == null) {
      for (var stack in stacks) {
        if (stack.steps != null) {
          for (var step in stack.steps!) {
            // Check if this step has inner components
            if (step.innnerComponents != null) {
              for (var component in step.innnerComponents!) {
                if (component.sentenceId == selectedSentenceId) {
                  selectedStep = component;
                  break;
                }
              }
              if (selectedStep != null) break;
            }
          }
        }
        if (selectedStep != null) break;
      }
    }

    // If no step found or no related keywords, show a message
    if (selectedStep == null ||
        selectedStep.relatedKeywords == null ||
        selectedStep.relatedKeywords!.isEmpty) {
      return Center(
        child: Text(
          'No related keywords found for this sentence.',
          style: TextStyle(
            color: _isDarkMode ? Colors.white70 : Colors.grey[700],
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    // Get the related keywords
    List<models.RelatedKeyword> relatedKeywords = selectedStep.relatedKeywords!;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: relatedKeywords.map((keywordData) {
          // Extract keyword and description from the RelatedKeyword object
          String keyword = keywordData.keyword ?? '';
          String description = keywordData.description ?? '';

          // Check if this keyword is used in the code
          final bool isUsed = _usedKeywords.contains(keyword);
          // Check if this keyword is currently selected
          final bool isSelected = _selectedKeyword == keyword;

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Keyword chip
                InkWell(
                  onTap: isUsed ? () => _highlightKeyword(keyword) : null,
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 6),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _isDarkMode
                          ? (const Color(0xFF4E5254))
                          : (Colors.grey[200]),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: isSelected
                            ? Colors.blue[700]!
                            : (_isDarkMode
                                ? (const Color(0xFF5E6264))
                                : (Colors.grey[400]!)),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Text(
                      keyword,
                      style: TextStyle(
                        color: (_isDarkMode
                            ? const Color(
                                0xFFCC7832) // Orange for used keywords in dark mode
                            : Colors.blue[
                                800]!) // Blue for used keywords in light mode
                        , // Grey for unused keywords in light mode
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ),

                // Description
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Text(
                    description,
                    style: TextStyle(
                      color: _isDarkMode ? Colors.white70 : Colors.grey[800],
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  // Widget to build the all keywords tab (current implementation)
  Widget _buildAllKeywordsTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _javaKeywords.entries.map((entry) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category header
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color:
                      _isDarkMode ? const Color(0xFF3C3F41) : Colors.blue[50],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  entry.key,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: _isDarkMode ? Colors.white : Colors.blue[800],
                  ),
                ),
              ),
              const SizedBox(height: 6),
              // Keywords in a row
              Padding(
                padding: const EdgeInsets.only(left: 12.0),
                child: Wrap(
                  children: entry.value.map((keyword) {
                    // Check if this keyword is used in the code
                    final bool isUsed = _usedKeywords.contains(keyword);
                    // Check if this keyword is currently selected
                    final bool isSelected = _selectedKeyword == keyword;

                    return GestureDetector(
                      onTap: isUsed ? () => _highlightKeyword(keyword) : null,
                      child: Container(
                        margin: const EdgeInsets.only(right: 6, bottom: 6),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 3),
                        decoration: BoxDecoration(
                          color: _isDarkMode
                              ? (isUsed
                                  ? const Color(0xFF4E5254)
                                  : const Color(0xFF3A3A3A))
                              : (isUsed ? Colors.grey[200] : Colors.grey[100]),
                          borderRadius: BorderRadius.circular(3),
                          border: Border.all(
                            color: isSelected
                                ? Colors.blue[700]!
                                : (_isDarkMode
                                    ? (isUsed
                                        ? const Color(0xFF5E6264)
                                        : const Color(0xFF4A4A4A))
                                    : (isUsed
                                        ? Colors.grey[400]!
                                        : Colors.grey[300]!)),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Text(
                          keyword,
                          style: TextStyle(
                            color: isUsed
                                ? (_isDarkMode
                                    ? const Color(
                                        0xFFCC7832) // Orange for used keywords in dark mode
                                    : Colors.blue[
                                        800]!) // Blue for used keywords in light mode
                                : (_isDarkMode
                                    ? Colors.grey[
                                        500] // Grey for unused keywords in dark mode
                                    : Colors.grey[
                                        600]), // Grey for unused keywords in light mode
                            fontWeight:
                                isUsed ? FontWeight.bold : FontWeight.normal,
                            fontSize: 12,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 12),
            ],
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('NSL Java Code Comparison'),
        actions: [
          // Theme toggle button
          IconButton(
              onPressed: () {
                setState(() {
                  showJavaKeywords = false;
                  selectedSentenceId = null;
                  _selectedKeyword = null;
                });
                // Scroll to top after state update
                Future.delayed(const Duration(milliseconds: 50), () {
                  if (_codeScrollController.hasClients) {
                    _codeScrollController.animateTo(
                      0,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                });
              },
              icon: Icon(Icons.refresh)),
          IconButton(
            icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
            tooltip:
                _isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',
            onPressed: _toggleThemeMode,
          ),
        ],
      ),
      body: code.isEmpty || stacks.isEmpty
          ? Center(child: CircularProgressIndicator())
          : ResizableSplitView(
              initialLeftPanelSize: 0.3, // 40% of the screen width
              minLeftPanelSize: 0.2, // Minimum 20% of the screen width
              maxLeftPanelSize: 0.6, // Maximum 60% of the screen width
              leftPanel: Container(
                  color: Colors.grey[50],
                  child: Column(children: [
                    // Left panel header
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      color: const Color(
                          0xFF3C3F41), // Same as right panel header color
                      child: Row(
                        children: [
                          const Icon(Icons.description,
                              color: Colors.white70, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            // Remove the .nsl extension if present
                            nslFileName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          // Container(
                          //   padding: const EdgeInsets.symmetric(
                          //       horizontal: 8, vertical: 2),
                          //   decoration: BoxDecoration(
                          //     color: Colors.green[700],
                          //     borderRadius: BorderRadius.circular(4),
                          //   ),
                          //   child: const Text(
                          //     'NSL',
                          //     style: TextStyle(
                          //       color: Colors.white,
                          //       fontSize: 12,
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    // Prescriptives metrics
                    Container(
                      // padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // First row of metrics
                          Row(
                            children: [
                              // Prescriptives
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.only(left: 8),
                                  // decoration: BoxDecoration(
                                  //   color: Colors.blue[100],
                                  //   borderRadius: BorderRadius.circular(8),
                                  //   border:
                                  //       Border.all(color: Colors.blue[300]!),
                                  // ),
                                  child: Row(
                                    children: [
                                      Text(
                                        'Prescriptives : ',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '$prescriptives',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              // Sub-Prescriptives
                              Expanded(
                                child: Container(
                                  // padding: const EdgeInsets.only(left: 8),
                                  // decoration: BoxDecoration(
                                  //   color: Colors.green[100],
                                  //   borderRadius: BorderRadius.circular(8),
                                  //   border:
                                  //       Border.all(color: Colors.green[300]!),
                                  // ),
                                  child: Row(
                                    children: [
                                      Text(
                                        'Sub-Prescriptive : ',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '$subPrescriptives',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          // Second row of metrics
                          Row(
                            children: [
                              // BETs
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.only(left: 8),
                                  // decoration: BoxDecoration(
                                  //   color: Colors.orange[100],
                                  //   borderRadius: BorderRadius.circular(8),
                                  //   border:
                                  //       Border.all(color: Colors.orange[300]!),
                                  // ),
                                  child: Row(
                                    children: [
                                      Text(
                                        'BETs : ',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '$bets',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              // Pathways
                              Expanded(
                                child: Container(
                                  // padding: const EdgeInsets.all(8),
                                  // decoration: BoxDecoration(
                                  //   color: Colors.purple[100],
                                  //   borderRadius: BorderRadius.circular(8),
                                  //   border:
                                  //       Border.all(color: Colors.purple[300]!),
                                  // ),
                                  child: Row(
                                    children: [
                                      Text(
                                        'Pathways : ',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '$pathways',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 5),
                    // List of stacks
                    Expanded(
                      child: ListView.builder(
                        itemCount: stacks.length,
                        itemBuilder: (context, stackIndex) {
                          var stack = stacks[stackIndex];
                          var stackName = stack.stackName ?? '';
                          var stackSteps = stack.steps ?? [];

                          return Theme(
                            data: Theme.of(context).copyWith(
                              dividerColor: Colors.grey[300],
                              dividerTheme: const DividerThemeData(
                                thickness: 0.5,
                                space: 0,
                              ),
                            ),
                            child: ExpansionTile(
                              initiallyExpanded: true,
                              title: Text(
                                stackName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              backgroundColor: Colors.grey[50],
                              collapsedBackgroundColor: Colors.blue[50],
                              textColor: Colors.blue[800],
                              children: stackSteps.map((step) {
                                // Get the color from the step
                                Color stepColor = Color(int.parse(
                                    '0xFF${step.color?.substring(1) ?? '000000'}'));

                                final bool isSelected =
                                    selectedSentenceId == step.sentenceId;

                                return Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                      left: BorderSide(
                                        color: isSelected
                                            ? stepColor
                                            : Colors.transparent,
                                        width: 4.0,
                                      ),
                                    ),
                                    color: isSelected
                                        ? stepColor.withAlpha(40)
                                        : Colors.transparent,
                                  ),
                                  child: ListTile(
                                    title: step.innnerComponents != null &&
                                            step.innnerComponents!.isNotEmpty
                                        ? _buildInnerComponentsText(
                                            step, isSelected)
                                        : Text(
                                            step.sentence ?? '',
                                            style: TextStyle(
                                              color: isSelected
                                                  ? Colors.black.withAlpha(220)
                                                  : Colors.black87,
                                              fontWeight: isSelected
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                            ),
                                          ),
                                    dense: true,
                                    // Only make the ListTile clickable if it doesn't have inner components
                                    onTap: step.innnerComponents != null &&
                                            step.innnerComponents!.isNotEmpty
                                        ? null
                                        : () {
                                            // Pre-fetch related lines before setState to improve performance
                                            final relatedLines =
                                                step.relatedLines;
                                            final firstLine =
                                                relatedLines != null &&
                                                        relatedLines.isNotEmpty
                                                    ? relatedLines[0]
                                                    : null;

                                            setState(() {
                                              // Do NOT clear keyword selection when selecting a sentence
                                              // Keep the _selectedKeyword as is
                                              selectedSentenceId =
                                                  step.sentenceId;
                                            });

                                            // Scroll immediately without delay if we have a line to scroll to
                                            if (firstLine != null) {
                                              _scrollToSelectedLine(firstLine);
                                            }
                                          },
                                    contentPadding: const EdgeInsets.only(
                                        left: 28.0, right: 16.0),
                                    leading: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: stepColor
                                            .withAlpha(51), // 0.2 * 255 = 51
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                            color: stepColor, width: 1),
                                      ),
                                      child: Text(
                                        step.id ?? '',
                                        style: TextStyle(
                                          color: stepColor,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          );
                        },
                      ),
                    )
                  ])),
              rightPanel: Container(
                height: MediaQuery.of(context).size.height,
                decoration: BoxDecoration(
                  color:
                      editorBackground, // Dynamic background color based on theme
                  border: Border.all(color: borderColor),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(_isDarkMode ? 100 : 50),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Editor header
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      color:
                          editorHeaderColor, // Dynamic header color based on theme
                      child: Row(
                        children: [
                          Icon(Icons.code, color: headerIconColor, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            flex: 3,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  javaFileName,
                                  style: TextStyle(
                                    color: headerTextColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                //  showJavaKeywords?
                                //   Container(
                                //     padding: const EdgeInsets.symmetric(
                                //         horizontal: 8, vertical: 2),
                                //     decoration: BoxDecoration(
                                //       color: Colors.blue[700],
                                //       borderRadius: BorderRadius.circular(4),
                                //     ),
                                //     child: const Text(
                                //       'Java',
                                //       style: TextStyle(
                                //         color: Colors.white,
                                //         fontSize: 12,
                                //       ),
                                //     ),
                                //   ):SizedBox(),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // !showJavaKeywords? Container(
                                //   padding: const EdgeInsets.symmetric(
                                //       horizontal: 8, vertical: 2),
                                //   decoration: BoxDecoration(
                                //     color: Colors.blue[700],
                                //     borderRadius: BorderRadius.circular(4),
                                //   ),
                                //   child: const Text(
                                //     'Java',
                                //     style: TextStyle(
                                //       color: Colors.white,
                                //       fontSize: 12,
                                //     ),
                                //   ),
                                // ):SizedBox(),
                                // Java keywords icon button
                                InkWell(
                                    key: _javaKeywordsButtonKey,
                                    onTap: () {
                                      setState(() {
                                        showJavaKeywords = !showJavaKeywords;
                                      });
                                    },
                                    child: Container(
                                      margin: const EdgeInsets.only(left: 4),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: _isDarkMode
                                            ? Colors.blue[800]
                                            : Colors.blue[600],
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: const Text(
                                        'Java Constructs',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                        ),
                                      ),
                                    )),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Code content with both horizontal and vertical scrolling
                    Expanded(
                      child: showJavaKeywords
                          ? ResizableSplitView(
                              initialLeftPanelSize:
                                  0.65, // 70% for code, 30% for keywords
                              minLeftPanelSize: 0.5, // Minimum 50% for code
                              maxLeftPanelSize: 0.85, // Maximum 85% for code
                              leftPanel: ScrollConfiguration(
                                behavior:
                                    ScrollConfiguration.of(context).copyWith(
                                  scrollbars: true,
                                  dragDevices: {
                                    PointerDeviceKind.mouse,
                                    PointerDeviceKind.touch,
                                    PointerDeviceKind.trackpad,
                                  },
                                ),
                                child: SingleChildScrollView(
                                  key: _codeViewKey,
                                  controller: _codeScrollController,
                                  scrollDirection: Axis.vertical,
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children:
                                            _buildCodeLines(selectedSentenceId),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              rightPanel: Container(
                                decoration: BoxDecoration(
                                  color: editorBackground,
                                  border: Border(
                                    left: BorderSide(
                                      color: borderColor,
                                      width: 1,
                                    ),
                                  ),
                                ),
                                child: javaKeywordsContainer(),
                              ),
                            )
                          : ScrollConfiguration(
                              behavior:
                                  ScrollConfiguration.of(context).copyWith(
                                scrollbars: true,
                                dragDevices: {
                                  PointerDeviceKind.mouse,
                                  PointerDeviceKind.touch,
                                  PointerDeviceKind.trackpad,
                                },
                              ),
                              child: SingleChildScrollView(
                                key: _codeViewKey,
                                controller: _codeScrollController,
                                scrollDirection: Axis.vertical,
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children:
                                          _buildCodeLines(selectedSentenceId),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}

/// A widget that creates a resizable split view with a draggable divider.
class ResizableSplitView extends StatefulWidget {
  final Widget leftPanel;
  final Widget rightPanel;
  final double initialLeftPanelSize;
  final double minLeftPanelSize;
  final double maxLeftPanelSize;

  const ResizableSplitView({
    super.key,
    required this.leftPanel,
    required this.rightPanel,
    this.initialLeftPanelSize = 0.3,
    this.minLeftPanelSize = 0.1,
    this.maxLeftPanelSize = 0.7,
  });

  @override
  State<ResizableSplitView> createState() => _ResizableSplitViewState();
}

class _ResizableSplitViewState extends State<ResizableSplitView>
    with SingleTickerProviderStateMixin {
  late double _leftPanelWidth;
  final double _dividerWidth = 6.0;
  bool _isDragging = false;

  // Animation controller for hover effect
  late AnimationController _hoverAnimationController;
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    _leftPanelWidth = widget.initialLeftPanelSize;

    // Initialize animation controller for hover effect
    _hoverAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _hoverAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final leftPanelPixelWidth = _leftPanelWidth * width;

        return Stack(
          children: [
            // Left panel
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              width: leftPanelPixelWidth,
              child: widget.leftPanel,
            ),

            // Divider
            Positioned(
              left: leftPanelPixelWidth - _dividerWidth / 2,
              top: 0,
              bottom: 0,
              width: _dividerWidth,
              child: MouseRegion(
                cursor: SystemMouseCursors.resizeLeftRight,
                onEnter: (_) {
                  setState(() {
                    _isHovering = true;
                  });
                  _hoverAnimationController.forward();
                },
                onExit: (_) {
                  setState(() {
                    _isHovering = false;
                  });
                  if (!_isDragging) {
                    _hoverAnimationController.reverse();
                  }
                },
                child: GestureDetector(
                  onHorizontalDragStart: (_) {
                    setState(() {
                      _isDragging = true;
                    });
                    _hoverAnimationController.forward();
                  },
                  onHorizontalDragUpdate: (details) {
                    setState(() {
                      // Calculate new width as a percentage of total width
                      final newLeftPanelWidth =
                          (_leftPanelWidth * width + details.delta.dx) / width;

                      // Constrain to min/max values
                      _leftPanelWidth = newLeftPanelWidth.clamp(
                        widget.minLeftPanelSize,
                        widget.maxLeftPanelSize,
                      );
                    });
                  },
                  onHorizontalDragEnd: (_) {
                    setState(() {
                      _isDragging = false;
                    });
                    if (!_isHovering) {
                      _hoverAnimationController.reverse();
                    }
                  },
                  child: AnimatedBuilder(
                    animation: _hoverAnimationController,
                    builder: (context, child) {
                      final double animValue = _hoverAnimationController.value;

                      return Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              Color.lerp(Colors.grey[300], Colors.blue[300],
                                  animValue)!,
                              Color.lerp(Colors.grey[400], Colors.blue[400],
                                  animValue)!,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Color.lerp(Colors.black.withAlpha(20),
                                  Colors.blue.withAlpha(60), animValue)!,
                              blurRadius: 3 + animValue * 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Top handle
                                  Container(
                                    height: constraints.maxHeight * 0.1,
                                    width: 4,
                                    decoration: BoxDecoration(
                                      color: Color.lerp(Colors.grey[200],
                                          Colors.white, animValue),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  SizedBox(height: 4 + animValue * 2),
                                  // Middle handle - larger
                                  Container(
                                    height: constraints.maxHeight *
                                        (0.2 + animValue * 0.05),
                                    width: 4,
                                    decoration: BoxDecoration(
                                      color: Color.lerp(Colors.grey[200],
                                          Colors.white, animValue),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  SizedBox(height: 4 + animValue * 2),
                                  // Bottom handle
                                  Container(
                                    height: constraints.maxHeight * 0.1,
                                    width: 4,
                                    decoration: BoxDecoration(
                                      color: Color.lerp(Colors.grey[200],
                                          Colors.white, animValue),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            // Right panel
            Positioned(
              left: leftPanelPixelWidth + _dividerWidth / 2,
              top: 0,
              bottom: 0,
              right: 0,
              child: widget.rightPanel,
            ),
          ],
        );
      },
    );
  }
}
