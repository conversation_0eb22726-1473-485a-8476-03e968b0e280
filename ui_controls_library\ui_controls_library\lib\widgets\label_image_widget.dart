import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A customizable widget that combines a label with an image.
///
/// This widget provides a rich set of customization options for displaying
/// text labels with images, including various layout options, styling, and
/// image configurations.
class LabelImageWidget extends StatefulWidget {
  /// The text to display as the label.
  final String text;

  /// The URL of the image to display.
  final String? imageUrl;

  /// The asset path of the image to display.
  final String? assetPath;

  /// The icon to display instead of an image.
  final IconData? icon;

  /// The layout direction of the widget (horizontal or vertical).
  final Axis direction;

  /// The position of the image relative to the text (before or after).
  final ImagePosition imagePosition;

  /// The width of the image.
  final double? imageWidth;

  /// The height of the image.
  final double? imageHeight;

  /// The height of the widget.
  final double? height;

  /// The width of the widget.
  final double? width;

  /// The fit mode for the image.
  final BoxFit imageFit;

  /// The border radius of the image.
  final double imageBorderRadius;

  /// Whether the image should be circular.
  final bool circularImage;

  /// The color of the image border.
  final Color? imageBorderColor;

  /// The width of the image border.
  final double imageBorderWidth;

  /// The background color of the image container.
  final Color? imageBackgroundColor;

  /// The padding around the image.
  final EdgeInsetsGeometry imagePadding;

  /// The margin around the image.
  final EdgeInsetsGeometry imageMargin;

  /// The style of the text.
  final TextStyle? textStyle;

  /// The color of the text.
  final Color? textColor;

  /// The font size of the text.
  final double? fontSize;

  /// The font weight of the text.
  final FontWeight? fontWeight;

  /// The font style of the text (normal or italic).
  final FontStyle? fontStyle;

  /// The alignment of the text.
  final TextAlign textAlign;

  /// The maximum number of lines for the text.
  final int? maxLines;

  /// Whether to overflow the text with an ellipsis.
  final bool overflow;

  /// The padding around the text.
  final EdgeInsetsGeometry textPadding;

  /// The margin around the text.
  final EdgeInsetsGeometry textMargin;

  /// The background color of the text container.
  final Color? textBackgroundColor;

  /// The border radius of the text container.
  final double textBorderRadius;

  /// The color of the text container border.
  final Color? textBorderColor;

  /// The width of the text container border.
  final double textBorderWidth;

  /// The spacing between the image and text.
  final double spacing;

  /// The alignment of the widget's content.
  final MainAxisAlignment mainAxisAlignment;

  /// The cross-axis alignment of the widget's content.
  final CrossAxisAlignment crossAxisAlignment;

  /// The background color of the widget.
  final Color? backgroundColor;

  /// The border radius of the widget.
  final double borderRadius;

  /// The color of the widget's border.
  final Color? borderColor;

  /// The width of the widget's border.
  final double borderWidth;

  /// The padding around the widget's content.
  final EdgeInsetsGeometry padding;

  /// The margin around the widget.
  final EdgeInsetsGeometry margin;

  /// Whether to show a shadow under the widget.
  final bool shadow;

  /// The elevation of the shadow.
  final double elevation;

  /// The color of the shadow.
  final Color? shadowColor;

  /// The tooltip text to show when hovering over the widget.
  final String? tooltip;

  /// The callback to execute when the widget is tapped.
  final VoidCallback? onTap;

  /// The callback to execute when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// The callback to execute when the image is tapped.
  final VoidCallback? onImageTap;

  /// The callback to execute when the text is tapped.
  final VoidCallback? onTextTap;

  /// Whether to make the text bold.
  final bool bold;

  /// Whether to make the text italic.
  final bool italic;

  /// Whether to make the text uppercase.
  final bool uppercase;

  /// Whether to make the text lowercase.
  final bool lowercase;

  /// Whether to capitalize the first letter of each word.
  final bool capitalize;

  /// Whether to show a placeholder when the image is loading.
  final bool showPlaceholder;

  /// The icon to use as a placeholder.
  final IconData placeholderIcon;

  /// The color of the placeholder icon.
  final Color? placeholderColor;

  /// The size of the placeholder icon.
  final double placeholderSize;

  /// Whether to show an error icon when the image fails to load.
  final bool showErrorIcon;

  /// The icon to use when the image fails to load.
  final IconData errorIcon;

  /// The color of the error icon.
  final Color? errorIconColor;

  /// The size of the error icon.
  final double errorIconSize;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  // Animation properties
  /// Whether to animate the widget when it changes
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Label-image-specific JSON configuration
  /// Whether to use JSON label-image configuration
  final bool useJsonLabelImageConfig;

  /// Label-image-specific JSON configuration
  final Map<String, dynamic>? labelImageConfig;

  /// Creates a label image widget.
  const LabelImageWidget({
    super.key,
    required this.text,
    this.imageUrl,
    this.assetPath,
    this.icon,
    this.direction = Axis.horizontal,
    this.imagePosition = ImagePosition.before,
    this.imageWidth,
    this.imageHeight,
    this.height,
    this.width,
    this.imageFit = BoxFit.cover,
    this.imageBorderRadius = 50.0,
    this.circularImage = false,
    this.imageBorderColor,
    this.imageBorderWidth = 0.0,
    this.imageBackgroundColor,
    this.imagePadding = EdgeInsets.zero,
    this.imageMargin = EdgeInsets.zero,
    this.textStyle,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.fontStyle,
    this.textAlign = TextAlign.start,
    this.maxLines,
    this.overflow = false,
    this.textPadding = const EdgeInsets.all(8.0),
    this.textMargin = EdgeInsets.zero,
    this.textBackgroundColor,
    this.textBorderRadius = 0.0,
    this.textBorderColor,
    this.textBorderWidth = 0.0,
    this.spacing = 8.0,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.backgroundColor,
    this.borderRadius = 0.0,
    this.borderColor,
    this.borderWidth = 0.0,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.shadow = false,
    this.elevation = 2.0,
    this.shadowColor,
    this.tooltip,
    this.onTap,
    this.onLongPress,
    this.onImageTap,
    this.onTextTap,
    this.bold = false,
    this.italic = false,
    this.uppercase = false,
    this.lowercase = false,
    this.capitalize = false,
    this.showPlaceholder = true,
    this.placeholderIcon = Icons.image,
    this.placeholderColor,
    this.placeholderSize = 24.0,
    this.showErrorIcon = true,
    this.errorIcon = Icons.broken_image,
    this.errorIconColor,
    this.errorIconSize = 24.0,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Label-image-specific JSON configuration
    this.useJsonLabelImageConfig = false,
    this.labelImageConfig,
  });

  /// Creates a LabelImageWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the LabelImageWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "text": "Hello World",
  ///   "imageUrl": "https://example.com/image.jpg",
  ///   "textColor": "#FF0000",
  ///   "bold": true
  /// }
  /// ```
  factory LabelImageWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return EdgeInsets.zero;
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return EdgeInsets.zero;
    }

    // Parse duration
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) {
        return const Duration(milliseconds: 300);
      }

      if (durationValue is int) {
        return Duration(milliseconds: durationValue);
      } else if (durationValue is Map<String, dynamic>) {
        final int milliseconds =
            (durationValue['milliseconds'] as num?)?.toInt() ?? 0;
        final int seconds = (durationValue['seconds'] as num?)?.toInt() ?? 0;
        final int minutes = (durationValue['minutes'] as num?)?.toInt() ?? 0;

        return Duration(
          milliseconds: milliseconds,
          seconds: seconds,
          minutes: minutes,
        );
      } else if (durationValue is String) {
        // Parse strings like "300ms", "2s", "1m"
        final RegExp durationRegExp = RegExp(r'(\d+)(ms|s|m)');
        final match = durationRegExp.firstMatch(durationValue);

        if (match != null) {
          final int value = int.parse(match.group(1)!);
          final String unit = match.group(2)!;

          switch (unit) {
            case 'ms':
              return Duration(milliseconds: value);
            case 's':
              return Duration(seconds: value);
            case 'm':
              return Duration(minutes: value);
            default:
              return const Duration(milliseconds: 300);
          }
        }
      }

      return const Duration(milliseconds: 300);
    }

    // Parse curve
    Curve parseCurve(dynamic curveValue) {
      if (curveValue == null) return Curves.easeInOut;

      if (curveValue is String) {
        switch (curveValue.toLowerCase()) {
          case 'linear':
            return Curves.linear;
          case 'decelerate':
            return Curves.decelerate;
          case 'ease':
            return Curves.ease;
          case 'easein':
          case 'ease_in':
            return Curves.easeIn;
          case 'easeout':
          case 'ease_out':
            return Curves.easeOut;
          case 'easeinout':
          case 'ease_in_out':
            return Curves.easeInOut;
          case 'elasticin':
          case 'elastic_in':
            return Curves.elasticIn;
          case 'elasticout':
          case 'elastic_out':
            return Curves.elasticOut;
          case 'elasticinout':
          case 'elastic_in_out':
            return Curves.elasticInOut;
          case 'bouncein':
          case 'bounce_in':
            return Curves.bounceIn;
          case 'bounceout':
          case 'bounce_out':
            return Curves.bounceOut;
          case 'bounceinout':
          case 'bounce_in_out':
            return Curves.bounceInOut;
          default:
            return Curves.easeInOut;
        }
      }

      return Curves.easeInOut;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final color = parseColor(styleValue['color']);
        final fontSize =
            styleValue['fontSize'] != null
                ? (styleValue['fontSize'] as num).toDouble()
                : null;
        final fontWeight =
            styleValue['fontWeight'] != null
                ? (styleValue['fontWeight'] == 'bold'
                    ? FontWeight.bold
                    : styleValue['fontWeight'] == 'normal'
                    ? FontWeight.normal
                    : FontWeight.normal)
                : null;
        final fontStyle =
            styleValue['fontStyle'] != null
                ? (styleValue['fontStyle'] == 'italic'
                    ? FontStyle.italic
                    : FontStyle.normal)
                : null;
        final letterSpacing =
            styleValue['letterSpacing'] != null
                ? (styleValue['letterSpacing'] as num).toDouble()
                : null;
        final wordSpacing =
            styleValue['wordSpacing'] != null
                ? (styleValue['wordSpacing'] as num).toDouble()
                : null;
        final height =
            styleValue['height'] != null
                ? (styleValue['height'] as num).toDouble()
                : null;
        final decoration =
            styleValue['decoration'] != null
                ? (styleValue['decoration'] == 'underline'
                    ? TextDecoration.underline
                    : styleValue['decoration'] == 'lineThrough'
                    ? TextDecoration.lineThrough
                    : styleValue['decoration'] == 'overline'
                    ? TextDecoration.overline
                    : null)
                : null;
        final fontFamily = styleValue['fontFamily'] as String?;

        return TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          wordSpacing: wordSpacing,
          height: height,
          decoration: decoration,
          fontFamily: fontFamily,
        );
      }

      return null;
    }

    // Parse text align
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'left':
            return TextAlign.left;
          case 'right':
            return TextAlign.right;
          case 'center':
            return TextAlign.center;
          case 'justify':
            return TextAlign.justify;
          case 'start':
            return TextAlign.start;
          case 'end':
            return TextAlign.end;
          default:
            return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight? parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return null;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'w100':
            return FontWeight.w100;
          case 'w200':
            return FontWeight.w200;
          case 'w300':
            return FontWeight.w300;
          case 'w400':
            return FontWeight.w400;
          case 'w500':
            return FontWeight.w500;
          case 'w600':
            return FontWeight.w600;
          case 'w700':
            return FontWeight.w700;
          case 'w800':
            return FontWeight.w800;
          case 'w900':
            return FontWeight.w900;
          default:
            return null;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse font style
    FontStyle? parseFontStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is String) {
        switch (styleValue.toLowerCase()) {
          case 'italic':
            return FontStyle.italic;
          case 'normal':
            return FontStyle.normal;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse box fit
    BoxFit parseBoxFit(dynamic fitValue) {
      if (fitValue == null) return BoxFit.cover;

      if (fitValue is String) {
        switch (fitValue.toLowerCase()) {
          case 'cover':
            return BoxFit.cover;
          case 'contain':
            return BoxFit.contain;
          case 'fill':
            return BoxFit.fill;
          case 'fitheight':
          case 'fit_height':
            return BoxFit.fitHeight;
          case 'fitwidth':
          case 'fit_width':
            return BoxFit.fitWidth;
          case 'none':
            return BoxFit.none;
          case 'scaledown':
          case 'scale_down':
            return BoxFit.scaleDown;
          default:
            return BoxFit.cover;
        }
      }

      return BoxFit.cover;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        // This is a simplified version. In a real app, you would need a more comprehensive mapping
        switch (iconValue.toLowerCase()) {
          case 'add':
            return Icons.add;
          case 'remove':
            return Icons.remove;
          case 'edit':
            return Icons.edit;
          case 'delete':
            return Icons.delete;
          case 'search':
            return Icons.search;
          case 'home':
            return Icons.home;
          case 'settings':
            return Icons.settings;
          case 'person':
            return Icons.person;
          case 'info':
            return Icons.info;
          case 'warning':
            return Icons.warning;
          case 'error':
            return Icons.error;
          case 'check':
            return Icons.check;
          case 'close':
            return Icons.close;
          case 'menu':
            return Icons.menu;
          case 'more':
            return Icons.more_vert;
          case 'arrow_back':
            return Icons.arrow_back;
          case 'arrow_forward':
            return Icons.arrow_forward;
          case 'arrow_up':
            return Icons.arrow_upward;
          case 'arrow_down':
            return Icons.arrow_downward;
          case 'star':
            return Icons.star;
          case 'favorite':
            return Icons.favorite;
          case 'thumb_up':
            return Icons.thumb_up;
          case 'thumb_down':
            return Icons.thumb_down;
          case 'image':
            return Icons.image;
          case 'broken_image':
            return Icons.broken_image;
          case 'photo':
            return Icons.photo;
          case 'camera':
            return Icons.camera_alt;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse image position
    ImagePosition parseImagePosition(dynamic positionValue) {
      if (positionValue == null) return ImagePosition.before;

      if (positionValue is String) {
        switch (positionValue.toLowerCase()) {
          case 'before':
            return ImagePosition.before;
          case 'after':
            return ImagePosition.after;
          default:
            return ImagePosition.before;
        }
      }

      return ImagePosition.before;
    }

    // Parse axis
    Axis parseAxis(dynamic axisValue) {
      if (axisValue == null) return Axis.horizontal;

      if (axisValue is String) {
        switch (axisValue.toLowerCase()) {
          case 'horizontal':
            return Axis.horizontal;
          case 'vertical':
            return Axis.vertical;
          default:
            return Axis.horizontal;
        }
      }

      return Axis.horizontal;
    }

    // Parse main axis alignment
    MainAxisAlignment parseMainAxisAlignment(dynamic alignmentValue) {
      if (alignmentValue == null) return MainAxisAlignment.start;

      if (alignmentValue is String) {
        switch (alignmentValue.toLowerCase()) {
          case 'start':
            return MainAxisAlignment.start;
          case 'end':
            return MainAxisAlignment.end;
          case 'center':
            return MainAxisAlignment.center;
          case 'spacearound':
          case 'space_around':
            return MainAxisAlignment.spaceAround;
          case 'spacebetween':
          case 'space_between':
            return MainAxisAlignment.spaceBetween;
          case 'spaceevenly':
          case 'space_evenly':
            return MainAxisAlignment.spaceEvenly;
          default:
            return MainAxisAlignment.start;
        }
      }

      return MainAxisAlignment.start;
    }

    // Parse cross axis alignment
    CrossAxisAlignment parseCrossAxisAlignment(dynamic alignmentValue) {
      if (alignmentValue == null) return CrossAxisAlignment.center;

      if (alignmentValue is String) {
        switch (alignmentValue.toLowerCase()) {
          case 'start':
            return CrossAxisAlignment.start;
          case 'end':
            return CrossAxisAlignment.end;
          case 'center':
            return CrossAxisAlignment.center;
          case 'stretch':
            return CrossAxisAlignment.stretch;
          case 'baseline':
            return CrossAxisAlignment.baseline;
          default:
            return CrossAxisAlignment.center;
        }
      }

      return CrossAxisAlignment.center;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onImageTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onImageTap'] = json['onImageTap'];
      useJsonCallbacks = true;
    }

    if (json['onTextTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTextTap'] = json['onTextTap'];
      useJsonCallbacks = true;
    }

    // Parse Label-image-specific configuration
    Map<String, dynamic>? labelImageConfig;
    bool useJsonLabelImageConfig =
        json['useJsonLabelImageConfig'] as bool? ?? false;

    if (json['labelImageConfig'] != null) {
      if (json['labelImageConfig'] is Map) {
        labelImageConfig = Map<String, dynamic>.from(
          json['labelImageConfig'] as Map,
        );
        useJsonLabelImageConfig = true;
      } else if (json['labelImageConfig'] is String) {
        try {
          labelImageConfig =
              jsonDecode(json['labelImageConfig'] as String)
                  as Map<String, dynamic>;
          useJsonLabelImageConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return LabelImageWidget(
      // Basic properties
      text: json['text'] as String? ?? '',
      imageUrl: json['imageUrl'] as String?,
      assetPath: json['assetPath'] as String?,
      icon: parseIconData(json['icon']),
      direction: parseAxis(json['direction']),
      imagePosition: parseImagePosition(json['imagePosition']),
      imageWidth:
          json['_getResponsiveAvatarWidth'] != null
              ? (json['_getResponsiveAvatarWidth'] as num).toDouble()
              : null,
      imageHeight:
          json['_getResponsiveAvatarWidth'] != null
              ? (json['_getResponsiveAvatarWidth'] as num).toDouble()
              : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      imageFit: parseBoxFit(json['imageFit']),
      imageBorderRadius:
          json['imageBorderRadius'] != null
              ? (json['imageBorderRadius'] as num).toDouble()
              : 50.0,
      circularImage: json['circularImage'] as bool? ?? false,
      imageBorderColor: parseColor(json['imageBorderColor']),
      imageBorderWidth:
          json['imageBorderWidth'] != null
              ? (json['imageBorderWidth'] as num).toDouble()
              : 0.0,
      imageBackgroundColor: parseColor(json['imageBackgroundColor']),
      imagePadding: parseEdgeInsets(json['imagePadding']),
      imageMargin: parseEdgeInsets(json['imageMargin']),
      textStyle: parseTextStyle(json['textStyle']),
      textColor: parseColor(json['textColor']),
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : null,
      fontWeight: parseFontWeight(json['fontWeight']),
      fontStyle: parseFontStyle(json['fontStyle']),
      textAlign: parseTextAlign(json['textAlign']),
      maxLines: json['maxLines'] as int?,
      overflow: json['overflow'] as bool? ?? false,
      textPadding: parseEdgeInsets(json['textPadding']),
      textMargin: parseEdgeInsets(json['textMargin']),
      textBackgroundColor: parseColor(json['textBackgroundColor']),
      textBorderRadius:
          json['textBorderRadius'] != null
              ? (json['textBorderRadius'] as num).toDouble()
              : 0.0,
      textBorderColor: parseColor(json['textBorderColor']),
      textBorderWidth:
          json['textBorderWidth'] != null
              ? (json['textBorderWidth'] as num).toDouble()
              : 0.0,
      spacing:
          json['spacing'] != null ? (json['spacing'] as num).toDouble() : 8.0,
      mainAxisAlignment: parseMainAxisAlignment(json['mainAxisAlignment']),
      crossAxisAlignment: parseCrossAxisAlignment(json['crossAxisAlignment']),
      backgroundColor: parseColor(json['backgroundColor']),
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 0.0,
      borderColor: parseColor(json['borderColor']),
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 0.0,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      shadow: json['shadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      shadowColor: parseColor(json['shadowColor']),
      tooltip: json['tooltip'] as String?,
      bold: json['bold'] as bool? ?? false,
      italic: json['italic'] as bool? ?? false,
      uppercase: json['uppercase'] as bool? ?? false,
      lowercase: json['lowercase'] as bool? ?? false,
      capitalize: json['capitalize'] as bool? ?? false,
      showPlaceholder: json['showPlaceholder'] as bool? ?? true,
      placeholderIcon: parseIconData(json['placeholderIcon']) ?? Icons.image,
      placeholderColor: parseColor(json['placeholderColor']),
      placeholderSize:
          json['placeholderSize'] != null
              ? (json['placeholderSize'] as num).toDouble()
              : 24.0,
      showErrorIcon: json['showErrorIcon'] as bool? ?? true,
      errorIcon: parseIconData(json['errorIcon']) ?? Icons.broken_image,
      errorIconColor: parseColor(json['errorIconColor']),
      errorIconSize:
          json['errorIconSize'] != null
              ? (json['errorIconSize'] as num).toDouble()
              : 24.0,

      // Advanced interaction properties
      onHover: null, // Cannot be created from JSON directly
      onFocus: null, // Cannot be created from JSON directly
      focusNode: null, // Cannot be created from JSON directly
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: null, // Cannot be created from JSON directly
      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: parseDuration(json['animationDuration']),
      animationCurve: parseCurve(json['animationCurve']),

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,

      // Label-image-specific JSON configuration
      useJsonLabelImageConfig: useJsonLabelImageConfig,
      labelImageConfig: labelImageConfig,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'text': text,
      'imageUrl': imageUrl,
      'assetPath': assetPath,
      'direction': direction == Axis.horizontal ? 'horizontal' : 'vertical',
      'imagePosition':
          imagePosition == ImagePosition.before ? 'before' : 'after',
      'imageWidth': imageWidth,
      'imageHeight': imageHeight,
      'height': height,
      'width': width,
      'imageFit': imageFit.toString().split('.').last,
      'imageBorderRadius': imageBorderRadius,
      'circularImage': circularImage,
      'imageBorderWidth': imageBorderWidth,
      'textAlign': textAlign.toString().split('.').last,
      'maxLines': maxLines,
      'overflow': overflow,
      'textBorderRadius': textBorderRadius,
      'textBorderWidth': textBorderWidth,
      'spacing': spacing,
      'borderRadius': borderRadius,
      'borderWidth': borderWidth,
      'shadow': shadow,
      'elevation': elevation,
      'bold': bold,
      'italic': italic,
      'uppercase': uppercase,
      'lowercase': lowercase,
      'capitalize': capitalize,
      'showPlaceholder': showPlaceholder,
      'placeholderSize': placeholderSize,
      'showErrorIcon': showErrorIcon,
      'errorIconSize': errorIconSize,

      // Colors
      'textColor': textColor != null ? '#${textColor!.toHexString()}' : null,
      'backgroundColor':
          backgroundColor != null ? '#${backgroundColor!.toHexString()}' : null,
      'borderColor':
          borderColor != null ? '#${borderColor!.toHexString()}' : null,
      'shadowColor':
          shadowColor != null ? '#${shadowColor!.toHexString()}' : null,
      'imageBorderColor':
          imageBorderColor != null
              ? '#${imageBorderColor!.toHexString()}'
              : null,
      'imageBackgroundColor':
          imageBackgroundColor != null
              ? '#${imageBackgroundColor!.toHexString()}'
              : null,
      'textBackgroundColor':
          textBackgroundColor != null
              ? '#${textBackgroundColor!.toHexString()}'
              : null,
      'textBorderColor':
          textBorderColor != null ? '#${textBorderColor!.toHexString()}' : null,
      'placeholderColor':
          placeholderColor != null
              ? '#${placeholderColor!.toHexString()}'
              : null,
      'errorIconColor':
          errorIconColor != null ? '#${errorIconColor!.toHexString()}' : null,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Advanced properties
      'tooltip': tooltip,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,

      // Animation properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonLabelImageConfig': useJsonLabelImageConfig,
    };
  }

  @override
  LabelImageWidgetState createState() => LabelImageWidgetState();
}

/// The position of the image relative to the text.
enum ImagePosition {
  /// The image is displayed before the text.
  before,

  /// The image is displayed after the text.
  after,
}

class LabelImageWidgetState extends State<LabelImageWidget>
    with SingleTickerProviderStateMixin {
  bool _isLoaded = false;
  bool _hasError = false;
  bool _isHovered = false;
  final bool _isFocused = false;

  // Animation controller for animated effects
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Initialize with full opacity if not animating
    if (!widget.hasAnimation) {
      _animationController.value = 1.0;
    } else {
      _animationController.forward();
    }

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void didUpdateWidget(LabelImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration and curve if changed
    if (oldWidget.animationDuration != widget.animationDuration) {
      _animationController.duration = widget.animationDuration;
    }

    if (oldWidget.animationCurve != widget.animationCurve) {
      _animation = CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      );
    }

    // Handle animation state changes
    if (!oldWidget.hasAnimation && widget.hasAnimation) {
      _animationController.forward(from: 0.0);
    } else if (oldWidget.hasAnimation && !widget.hasAnimation) {
      _animationController.value = 1.0;
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Process text transformations
    String displayText = widget.text;
    if (widget.uppercase) {
      displayText = displayText.toUpperCase();
    } else if (widget.lowercase) {
      displayText = displayText.toLowerCase();
    } else if (widget.capitalize) {
      displayText = displayText
          .split(' ')
          .map((word) {
            if (word.isEmpty) return word;
            return word[0].toUpperCase() + word.substring(1).toLowerCase();
          })
          .join(' ');
    }

    // Create text style
    TextStyle effectiveTextStyle =
        widget.textStyle ??
        Theme.of(context).textTheme.bodyMedium ??
        const TextStyle();

    // Apply text style properties
    if (widget.fontSize != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        fontSize: widget.fontSize,
      );
    }

    if (widget.fontWeight != null || widget.bold) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        fontWeight: widget.fontWeight ?? (widget.bold ? FontWeight.bold : null),
      );
    }

    if (widget.fontStyle != null || widget.italic) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        fontStyle:
            widget.fontStyle ?? (widget.italic ? FontStyle.italic : null),
      );
    }

    if (widget.textColor != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(color: widget.textColor);
    }

    // Create the text widget
    Widget textWidget = Text(
      displayText,

      // style: TextStyle(
      //   color: Colors.black,
      //   fontSize: _getResponsiveValueFontSize(context),
      //   fontWeight: FontWeight.w500,
      // ),
      style: FontManager.getCustomStyle(
        fontFamily: FontManager.fontFamilyInter,
        fontWeight: FontManager.medium,
        fontSize: _getResponsiveValueFontSize(context),
        color: const Color(0xFF333333),
      ),
      textAlign: widget.textAlign,
      maxLines: widget.maxLines,
      overflow: widget.overflow ? TextOverflow.ellipsis : TextOverflow.visible,
      softWrap: true,
    );

    // Apply text container styling
    Widget textContainer = Container(
      padding: widget.textPadding,
      margin: widget.textMargin,
      decoration: BoxDecoration(
        color: widget.textBackgroundColor,
        borderRadius: BorderRadius.circular(widget.textBorderRadius),
        border:
            widget.textBorderColor != null
                ? Border.all(
                  color: widget.textBorderColor!,
                  width: widget.textBorderWidth,
                )
                : null,
      ),
      child: textWidget,
    );

    // Make text tappable if needed
    if (widget.onTextTap != null) {
      textContainer = InkWell(
        onTap: widget.onTextTap,
        borderRadius: BorderRadius.circular(widget.textBorderRadius),
        child: textContainer,
      );
    }

    // Create the image widget
    Widget imageWidget = _buildImageWidget();

    // Apply image container styling
    Widget imageContainer = Container(
      width: widget.imageWidth,
      height: widget.imageHeight ?? widget.height,
      padding: widget.imagePadding,
      margin: widget.imageMargin,
      decoration: BoxDecoration(
        color: widget.imageBackgroundColor,
        borderRadius: widget.circularImage ? null : BorderRadius.circular(50.0),
        //shape: widget.circularImage ? BoxShape.circle : BoxShape.rectangle,
        border:
            widget.imageBorderColor != null
                ? Border.all(
                  color: widget.imageBorderColor!,
                  width: widget.imageBorderWidth,
                )
                : null,
      ),
      child: ClipRRect(
        // borderRadius:
        //     widget.circularImage
        //         ? BorderRadius.circular(1000) // Large value to ensure circle
        //         : BorderRadius.circular(widget.imageBorderRadius),
        borderRadius: BorderRadius.circular(50.0),
        child: imageWidget,
      ),
    );

    // Make image tappable if needed
    if (widget.onImageTap != null) {
      imageContainer = InkWell(
        onTap: widget.onImageTap,
        borderRadius:
            widget.circularImage
                ? BorderRadius.circular(1000)
                : BorderRadius.circular(widget.imageBorderRadius),
        child: imageContainer,
      );
    }

    // Arrange image and text based on direction and position
    List<Widget> children = [];
    if (widget.direction == Axis.horizontal) {
      if (widget.imagePosition == ImagePosition.before) {
        children = [
          imageContainer,
          SizedBox(width: widget.spacing),
          Expanded(child: textContainer),
        ];
      } else {
        children = [
          Expanded(child: textContainer),
          SizedBox(width: widget.spacing),
          imageContainer,
        ];
      }
    } else {
      if (widget.imagePosition == ImagePosition.before) {
        children = [
          imageContainer,
          SizedBox(height: widget.spacing),
          textContainer,
        ];
      } else {
        children = [
          textContainer,
          SizedBox(height: widget.spacing),
          imageContainer,
        ];
      }
    }

    // Create the main layout
    Widget content =
        widget.direction == Axis.horizontal
            ? Row(
              mainAxisAlignment: widget.mainAxisAlignment,
              crossAxisAlignment: widget.crossAxisAlignment,
              mainAxisSize: MainAxisSize.min,
              children: children,
            )
            : Column(
              mainAxisAlignment: widget.mainAxisAlignment,
              crossAxisAlignment: widget.crossAxisAlignment,
              mainAxisSize: MainAxisSize.min,
              children: children,
            );

    // Apply container styling
    Widget containerWidget = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.borderColor != null
                ? Border.all(
                  color: widget.borderColor!,
                  width: widget.borderWidth,
                )
                : null,
        boxShadow:
            widget.shadow
                ? [
                  BoxShadow(
                    color: widget.shadowColor ?? Colors.black.withAlpha(51),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 1),
                  ),
                ]
                : null,
      ),
      child: content,
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      containerWidget = FadeTransition(
        opacity: _animation,
        child: containerWidget,
      );
    }

    // Apply advanced interaction properties
    if (widget.onHover != null ||
        widget.onFocus != null ||
        widget.onDoubleTap != null ||
        widget.onTap != null ||
        widget.onLongPress != null) {
      containerWidget = MouseRegion(
        onEnter: (event) {
          if (widget.onHover != null) {
            setState(() {
              _isHovered = true;
            });
            widget.onHover!(true);
          }
        },
        onExit: (event) {
          if (widget.onHover != null) {
            setState(() {
              _isHovered = false;
            });
            widget.onHover!(false);
          }
        },
        cursor:
            widget.onTap != null ? SystemMouseCursors.click : MouseCursor.defer,
        child: GestureDetector(
          onTap:
              widget.onTap != null
                  ? () {
                    // Execute onTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onTap')) {
                      _executeJsonCallback('onTap');
                    }

                    // Call standard callback
                    widget.onTap!();
                  }
                  : null,
          onDoubleTap:
              widget.onDoubleTap != null
                  ? () {
                    // Execute onDoubleTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                      _executeJsonCallback('onDoubleTap');
                    }

                    // Call standard callback
                    widget.onDoubleTap!();
                  }
                  : null,
          onLongPress:
              widget.onLongPress != null
                  ? () {
                    // Execute onLongPress callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onLongPress')) {
                      _executeJsonCallback('onLongPress');
                    }

                    // Call standard callback
                    widget.onLongPress!();
                  }
                  : null,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: widget.onFocus,
            child: containerWidget,
          ),
        ),
      );
    }

    // Add tooltip if needed
    if (widget.tooltip != null) {
      containerWidget = Tooltip(
        message: widget.tooltip!,
        child: containerWidget,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      containerWidget = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: containerWidget,
      );
    }

    return containerWidget;
  }

  Widget _buildImageWidget() {
    // If an icon is provided, use it instead of an image
    if (widget.icon != null) {
      return Icon(
        widget.icon!,
        size: widget.imageWidth ?? 24.0,
        color: widget.textColor,
      );
    }

    // If a URL is provided, load the image from the network
    if (widget.imageUrl != null) {
      return Image.network(
        widget.imageUrl!,
        fit: widget.imageFit,
        width: widget.imageWidth,
        height: widget.imageHeight ?? widget.height,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            // Image loaded successfully
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && !_isLoaded) {
                setState(() {
                  _isLoaded = true;
                  _hasError = false;
                });
              }
            });
            return child;
          }
          return _buildPlaceholder();
        },
        errorBuilder: (context, error, stackTrace) {
          // Image failed to load
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && !_isLoaded) {
              setState(() {
                _isLoaded = true;
                _hasError = true;
              });
            }
          });
          return _buildErrorWidget();
        },
      );
    }

    // If an asset path is provided, load the image from assets
    if (widget.assetPath != null) {
      try {
        return Image.asset(
          widget.assetPath!,
          fit: widget.imageFit,
          width: widget.imageWidth,
          height: widget.imageHeight ?? widget.height,
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorWidget();
          },
        );
      } catch (e) {
        return _buildErrorWidget();
      }
    }

    // If no image source is provided, show a placeholder
    return _buildPlaceholder();
  }

  Widget _buildPlaceholder() {
    if (!widget.showPlaceholder) {
      return SizedBox(
        width: widget.imageWidth ?? 24.0,
        height: widget.imageHeight ?? widget.height ?? 24.0,
      );
    }

    return Container(
      width: widget.imageWidth ?? 24.0,
      height: widget.imageHeight ?? widget.height ?? 24.0,
      color: widget.imageBackgroundColor ?? Colors.grey.shade200,
      child: Center(
        child: Icon(
          widget.placeholderIcon,
          size: widget.placeholderSize,
          color: widget.placeholderColor ?? Colors.grey.shade400,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (!widget.showErrorIcon) {
      return SizedBox(
        width: widget.imageWidth ?? 24.0,
        height: widget.imageHeight ?? widget.height ?? 24.0,
      );
    }

    return Container(
      //width: widget.imageWidth ?? 24.0,
      //height: widget.imageHeight ?? widget.height ?? 24.0,
      width: _getResponsiveAvatarWidth(context),
      height: _getResponsiveAvatarWidth(context),
      color: widget.imageBackgroundColor ?? Color(0xff0058FF),
      child: Center(
        child: Icon(
          widget.errorIcon,
          size: widget.errorIconSize,
          color: widget.errorIconColor ?? Colors.white,
        ),
      ),
    );
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}

double _getResponsiveAvatarWidth(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 48.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 40.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}
