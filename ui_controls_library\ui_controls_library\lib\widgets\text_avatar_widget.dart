import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A customizable text avatar widget that displays initials or text in a styled container.
///
/// This widget provides various customization options including shape, size, colors,
/// text style, and behavior.
class TextAvatarWidget extends StatelessWidget {
  /// The text to display in the avatar.
  /// If null, initials will be generated from [name].
  final String? text;

  /// The name to generate initials from if [text] is null.
  final String? name;

  /// The shape of the avatar.
  final AvatarShape shape;

  /// The size of the avatar.
  final double size;

  /// The background color of the avatar.
  /// If null, a random color will be generated based on the text/name.
  final Color? backgroundColor;

  /// The text color of the avatar.
  final Color textColor;

  /// The border color of the avatar.
  final Color? borderColor;

  /// The border width of the avatar.
  final double borderWidth;

  /// The font size of the text.
  /// If null, it will be calculated based on the avatar size.
  final double? fontSize;

  /// The font weight of the text.
  final FontWeight fontWeight;

  /// The font family of the text.
  final String? fontFamily;

  /// The maximum number of characters to display.
  final int maxChars;

  /// Whether to use uppercase text.
  final bool uppercase;

  /// Whether to show a shadow under the avatar.
  final bool showShadow;

  /// The shadow color.
  final Color shadowColor;

  /// The shadow elevation.
  final double shadowElevation;

  /// The shadow blur radius.
  final double shadowBlurRadius;

  /// The shadow offset.
  final Offset shadowOffset;

  /// Whether to show a badge.
  final bool showBadge;

  /// The badge color.
  final Color badgeColor;

  /// The badge size.
  final double badgeSize;

  /// The badge position.
  final BadgePosition badgePosition;

  /// The badge content.
  final Widget? badgeContent;

  /// The badge border color.
  final Color badgeBorderColor;

  /// The badge border width.
  final double badgeBorderWidth;

  /// Whether to show a tooltip.
  final bool showTooltip;

  /// The tooltip message.
  final String? tooltipMessage;

  /// Whether to show a glow effect.
  final bool showGlow;

  /// The glow color.
  final Color glowColor;

  /// The glow radius.
  final double glowRadius;

  /// Whether to show a gradient background.
  final bool showGradient;

  /// The gradient colors.
  final List<Color>? gradientColors;

  /// The gradient begin alignment.
  final Alignment gradientBegin;

  /// The gradient end alignment.
  final Alignment gradientEnd;

  /// The onTap callback.
  final VoidCallback? onTap;

  /// The onLongPress callback.
  final VoidCallback? onLongPress;

  /// Whether to show a ripple effect when tapped.
  final bool showRipple;

  /// The ripple color.
  final Color? rippleColor;

  /// Whether to show a hover effect.
  final bool showHover;

  /// The hover color.
  final Color? hoverColor;

  /// Whether to show a focus effect.
  final bool showFocus;

  /// The focus color.
  final Color? focusColor;

  /// Whether to show a pressed effect.
  final bool showPressed;

  /// The pressed color.
  final Color? pressedColor;

  // Additional Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHoverChanged;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocusChanged;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// The avatar style.
  final AvatarStyle style;

  const TextAvatarWidget({
    super.key,
    this.text,
    this.name,
    this.shape = AvatarShape.circle,
    this.size = 40.0,
    this.backgroundColor,
    this.textColor = Colors.white,
    this.borderColor,
    this.borderWidth = 0.0,
    this.fontSize,
    this.fontWeight = FontManager.bold,
    this.fontFamily = FontManager.fontFamilyInter,
    this.maxChars = 2,
    this.uppercase = true,
    this.showShadow = false,
    this.shadowColor = Colors.black26,
    this.shadowElevation = 2.0,
    this.shadowBlurRadius = 4.0,
    this.shadowOffset = const Offset(0, 2),
    this.showBadge = false,
    this.badgeColor = Colors.red,
    this.badgeSize = 10.0,
    this.badgePosition = BadgePosition.topRight,
    this.badgeContent,
    this.badgeBorderColor = Colors.white,
    this.badgeBorderWidth = 1.0,
    this.showTooltip = false,
    this.tooltipMessage,
    this.showGlow = false,
    this.glowColor = Colors.blue,
    this.glowRadius = 5.0,
    this.showGradient = false,
    this.gradientColors,
    this.gradientBegin = Alignment.topLeft,
    this.gradientEnd = Alignment.bottomRight,
    this.onTap,
    this.onLongPress,
    this.showRipple = true,
    this.rippleColor,
    this.showHover = true,
    this.hoverColor,
    this.showFocus = true,
    this.focusColor,
    this.showPressed = true,
    this.pressedColor,
    this.style = AvatarStyle.flat,
    this.onHoverChanged,
    this.onFocusChanged,
    this.focusNode,
    this.autofocus = false,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onDoubleTap,
  });

  /// Creates a TextAvatarWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the TextAvatarWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "text": "AB",
  ///   "name": "Alice Brown",
  ///   "shape": "circle",
  ///   "size": 50.0,
  ///   "backgroundColor": "blue",
  ///   "textColor": "white"
  /// }
  /// ```
  factory TextAvatarWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          case 'teal':
            return Colors.teal;
          case 'cyan':
            return Colors.cyan;
          case 'amber':
            return Colors.amber;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse(
                  '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
                );
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.bold;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          case 'thin':
            return FontWeight.w200;
          case 'medium':
            return FontWeight.w500;
          case 'semibold':
            return FontWeight.w600;
          case 'extrabold':
            return FontWeight.w800;
          case 'black':
            return FontWeight.w900;
          default:
            return FontWeight.bold;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.bold;
        }
      }

      return FontWeight.bold;
    }

    // Parse alignment
    Alignment parseAlignment(dynamic alignmentValue) {
      if (alignmentValue == null) return Alignment.topLeft;

      if (alignmentValue is String) {
        switch (alignmentValue.toLowerCase()) {
          case 'center':
            return Alignment.center;
          case 'topleft':
          case 'top_left':
            return Alignment.topLeft;
          case 'topright':
          case 'top_right':
            return Alignment.topRight;
          case 'bottomleft':
          case 'bottom_left':
            return Alignment.bottomLeft;
          case 'bottomright':
          case 'bottom_right':
            return Alignment.bottomRight;
          case 'top':
          case 'topcenter':
          case 'top_center':
            return Alignment.topCenter;
          case 'bottom':
          case 'bottomcenter':
          case 'bottom_center':
            return Alignment.bottomCenter;
          case 'left':
          case 'centerleft':
          case 'center_left':
            return Alignment.centerLeft;
          case 'right':
          case 'centerright':
          case 'center_right':
            return Alignment.centerRight;
          default:
            return Alignment.topLeft;
        }
      }

      return Alignment.topLeft;
    }

    // Parse offset
    Offset parseOffset(dynamic offsetValue) {
      if (offsetValue == null) return const Offset(0, 2);

      if (offsetValue is Map<String, dynamic>) {
        final dx = (offsetValue['dx'] as num?)?.toDouble() ?? 0.0;
        final dy = (offsetValue['dy'] as num?)?.toDouble() ?? 2.0;
        return Offset(dx, dy);
      } else if (offsetValue is List && offsetValue.length >= 2) {
        final dx = (offsetValue[0] as num?)?.toDouble() ?? 0.0;
        final dy = (offsetValue[1] as num?)?.toDouble() ?? 2.0;
        return Offset(dx, dy);
      }

      return const Offset(0, 2);
    }

    // Parse avatar shape
    AvatarShape parseShape(dynamic shapeValue) {
      if (shapeValue == null) return AvatarShape.circle;

      if (shapeValue is String) {
        switch (shapeValue.toLowerCase()) {
          case 'circle':
            return AvatarShape.circle;
          case 'square':
            return AvatarShape.square;
          case 'rounded':
          case 'rounded_square':
          case 'rounded_rectangle':
            return AvatarShape.rounded;
          default:
            return AvatarShape.circle;
        }
      } else if (shapeValue is int) {
        switch (shapeValue) {
          case 0:
            return AvatarShape.circle;
          case 1:
            return AvatarShape.square;
          case 2:
            return AvatarShape.rounded;
          default:
            return AvatarShape.circle;
        }
      }

      return AvatarShape.circle;
    }

    // Parse badge position
    BadgePosition parseBadgePosition(dynamic positionValue) {
      if (positionValue == null) return BadgePosition.topRight;

      if (positionValue is String) {
        switch (positionValue.toLowerCase()) {
          case 'topright':
          case 'top_right':
            return BadgePosition.topRight;
          case 'topleft':
          case 'top_left':
            return BadgePosition.topLeft;
          case 'bottomright':
          case 'bottom_right':
            return BadgePosition.bottomRight;
          case 'bottomleft':
          case 'bottom_left':
            return BadgePosition.bottomLeft;
          default:
            return BadgePosition.topRight;
        }
      } else if (positionValue is int) {
        switch (positionValue) {
          case 0:
            return BadgePosition.topRight;
          case 1:
            return BadgePosition.topLeft;
          case 2:
            return BadgePosition.bottomRight;
          case 3:
            return BadgePosition.bottomLeft;
          default:
            return BadgePosition.topRight;
        }
      }

      return BadgePosition.topRight;
    }

    // Parse avatar style
    AvatarStyle parseStyle(dynamic styleValue) {
      if (styleValue == null) return AvatarStyle.flat;

      if (styleValue is String) {
        switch (styleValue.toLowerCase()) {
          case 'flat':
            return AvatarStyle.flat;
          case 'material':
            return AvatarStyle.material;
          case 'elevated':
            return AvatarStyle.elevated;
          default:
            return AvatarStyle.flat;
        }
      } else if (styleValue is int) {
        switch (styleValue) {
          case 0:
            return AvatarStyle.flat;
          case 1:
            return AvatarStyle.material;
          case 2:
            return AvatarStyle.elevated;
          default:
            return AvatarStyle.flat;
        }
      }

      return AvatarStyle.flat;
    }

    // Parse gradient colors
    List<Color>? parseGradientColors(dynamic colorsValue) {
      if (colorsValue == null) return null;

      if (colorsValue is List) {
        final colors = <Color>[];
        for (final colorValue in colorsValue) {
          final color = parseColor(colorValue);
          if (color != null) {
            colors.add(color);
          }
        }
        return colors.isNotEmpty ? colors : null;
      }

      return null;
    }

    return TextAvatarWidget(
      text: json['text'] as String?,
      name: json['name'] as String?,
      shape: parseShape(json['shape']),
      size: (json['size'] as num?)?.toDouble() ?? 40.0,
      backgroundColor: parseColor(json['backgroundColor']),
      textColor: parseColor(json['textColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 0.0,
      fontSize: (json['fontSize'] as num?)?.toDouble(),
      fontWeight: parseFontWeight(json['fontWeight']),
      fontFamily: json['fontFamily'] as String?,
      maxChars: json['maxChars'] as int? ?? 2,
      uppercase: json['uppercase'] as bool? ?? true,
      showShadow: json['showShadow'] as bool? ?? false,
      shadowColor: parseColor(json['shadowColor']) ?? Colors.black26,
      shadowElevation: (json['shadowElevation'] as num?)?.toDouble() ?? 2.0,
      shadowBlurRadius: (json['shadowBlurRadius'] as num?)?.toDouble() ?? 4.0,
      shadowOffset: parseOffset(json['shadowOffset']),
      showBadge: json['showBadge'] as bool? ?? false,
      badgeColor: parseColor(json['badgeColor']) ?? Colors.red,
      badgeSize: (json['badgeSize'] as num?)?.toDouble() ?? 10.0,
      badgePosition: parseBadgePosition(json['badgePosition']),
      badgeContent: null, // Cannot be created from JSON
      badgeBorderColor: parseColor(json['badgeBorderColor']) ?? Colors.white,
      badgeBorderWidth: (json['badgeBorderWidth'] as num?)?.toDouble() ?? 1.0,
      showTooltip: json['showTooltip'] as bool? ?? false,
      tooltipMessage: json['tooltipMessage'] as String?,
      showGlow: json['showGlow'] as bool? ?? false,
      glowColor: parseColor(json['glowColor']) ?? Colors.blue,
      glowRadius: (json['glowRadius'] as num?)?.toDouble() ?? 5.0,
      showGradient: json['showGradient'] as bool? ?? false,
      gradientColors: parseGradientColors(json['gradientColors']),
      gradientBegin: parseAlignment(json['gradientBegin']),
      gradientEnd: parseAlignment(json['gradientEnd']),
      onTap:
          json['onTap'] == true
              ? () {
                debugPrint('Avatar tapped');
              }
              : null,
      onLongPress:
          json['onLongPress'] == true
              ? () {
                debugPrint('Avatar long pressed');
              }
              : null,
      showRipple: json['showRipple'] as bool? ?? true,
      rippleColor: parseColor(json['rippleColor']),
      showHover: json['showHover'] as bool? ?? true,
      hoverColor: parseColor(json['hoverColor']),
      showFocus: json['showFocus'] as bool? ?? true,
      focusColor: parseColor(json['focusColor']),
      showPressed: json['showPressed'] as bool? ?? true,
      pressedColor: parseColor(json['pressedColor']),
      style: parseStyle(json['style']),
      onHoverChanged:
          json['onHoverChanged'] == true
              ? (isHovered) {
                debugPrint('Avatar hover: $isHovered');
              }
              : null,
      onFocusChanged:
          json['onFocusChanged'] == true
              ? (isFocused) {
                debugPrint('Avatar focus: $isFocused');
              }
              : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      semanticsLabel: json['semanticsLabel'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap:
          json['onDoubleTap'] == true
              ? () {
                debugPrint('Avatar double tapped');
              }
              : null,
    );
  }

  /// Converts the TextAvatarWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'shape': _shapeToString(shape),
      'size': size,
      'textColor': _colorToString(textColor),
      'maxChars': maxChars,
      'uppercase': uppercase,
      'showShadow': showShadow,
      'shadowColor': _colorToString(shadowColor),
      'shadowElevation': shadowElevation,
      'shadowBlurRadius': shadowBlurRadius,
      'showBadge': showBadge,
      'badgeColor': _colorToString(badgeColor),
      'badgeSize': badgeSize,
      'badgePosition': _badgePositionToString(badgePosition),
      'badgeBorderColor': _colorToString(badgeBorderColor),
      'badgeBorderWidth': badgeBorderWidth,
      'showTooltip': showTooltip,
      'showGlow': showGlow,
      'glowColor': _colorToString(glowColor),
      'glowRadius': glowRadius,
      'showGradient': showGradient,
      'gradientBegin': _alignmentToString(gradientBegin),
      'gradientEnd': _alignmentToString(gradientEnd),
      'showRipple': showRipple,
      'showHover': showHover,
      'showFocus': showFocus,
      'showPressed': showPressed,
      'style': _styleToString(style),
      'borderWidth': borderWidth,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties
    if (text != null) json['text'] = text;
    if (name != null) json['name'] = name;
    if (backgroundColor != null)
      json['backgroundColor'] = _colorToString(backgroundColor!);
    if (borderColor != null) json['borderColor'] = _colorToString(borderColor!);
    if (fontSize != null) json['fontSize'] = fontSize;
    if (fontFamily != null) json['fontFamily'] = fontFamily;
    if (tooltipMessage != null) json['tooltipMessage'] = tooltipMessage;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;
    if (rippleColor != null) json['rippleColor'] = _colorToString(rippleColor!);
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);
    if (pressedColor != null)
      json['pressedColor'] = _colorToString(pressedColor!);

    // Add font weight
    json['fontWeight'] = _fontWeightToString(fontWeight);

    // Add shadow offset
    json['shadowOffset'] = {'dx': shadowOffset.dx, 'dy': shadowOffset.dy};

    // Add gradient colors if available
    if (gradientColors != null && gradientColors!.isNotEmpty) {
      json['gradientColors'] =
          gradientColors!.map((color) => _colorToString(color)).toList();
    }

    // Add callback flags
    if (onTap != null) json['onTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;
    if (onHoverChanged != null) json['onHoverChanged'] = true;
    if (onFocusChanged != null) json['onFocusChanged'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';
    if (color == Colors.teal) return 'teal';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Helper method to convert a FontWeight to a string
  static String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.bold) return 'bold';
    if (weight == FontWeight.normal) return 'normal';
    if (weight == FontWeight.w100) return '100';
    if (weight == FontWeight.w200) return '200';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w400) return 'normal';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w700) return 'bold';
    if (weight == FontWeight.w800) return 'extrabold';
    if (weight == FontWeight.w900) return 'black';

    return 'bold';
  }

  /// Helper method to convert an AvatarShape to a string
  static String _shapeToString(AvatarShape shape) {
    switch (shape) {
      case AvatarShape.circle:
        return 'circle';
      case AvatarShape.square:
        return 'square';
      case AvatarShape.rounded:
        return 'rounded';
    }
  }

  /// Helper method to convert a BadgePosition to a string
  static String _badgePositionToString(BadgePosition position) {
    switch (position) {
      case BadgePosition.topRight:
        return 'topRight';
      case BadgePosition.topLeft:
        return 'topLeft';
      case BadgePosition.bottomRight:
        return 'bottomRight';
      case BadgePosition.bottomLeft:
        return 'bottomLeft';
    }
  }

  /// Helper method to convert an AvatarStyle to a string
  static String _styleToString(AvatarStyle style) {
    switch (style) {
      case AvatarStyle.flat:
        return 'flat';
      case AvatarStyle.material:
        return 'material';
      case AvatarStyle.elevated:
        return 'elevated';
    }
  }

  /// Helper method to convert an Alignment to a string
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.topLeft) return 'topLeft';
    if (alignment == Alignment.topCenter) return 'top';
    if (alignment == Alignment.topRight) return 'topRight';
    if (alignment == Alignment.centerLeft) return 'left';
    if (alignment == Alignment.centerRight) return 'right';
    if (alignment == Alignment.bottomLeft) return 'bottomLeft';
    if (alignment == Alignment.bottomCenter) return 'bottom';
    if (alignment == Alignment.bottomRight) return 'bottomRight';

    return 'center';
  }

  @override
  Widget build(BuildContext context) {
    // Generate display text
    final displayText = _generateDisplayText();

    // Calculate font size if not provided
    final calculatedFontSize = fontSize ?? size * 0.4;

    // Generate background color if not provided
    final calculatedBackgroundColor =
        backgroundColor ?? _generateColorFromText(displayText);

    // Create text style
    final textStyle = TextStyle(
      color: textColor,
      fontSize: calculatedFontSize,
      fontWeight: fontWeight,
      fontFamily: fontFamily,
    );

    // Create the avatar content
    Widget avatarContent = Center(
      child: Text(
        displayText,
        // style: TextStyle(
        //   fontWeight: FontWeight.w500,
        //   fontFamily: 'Inter',
        //   fontSize: _getResponsiveFontSize(context),
        //   color: Colors.white,
        // ),
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          color: Colors.white,
          fontSize: _getResponsiveFontSize(context),
        ),
        textAlign: TextAlign.center,
      ),
    );

    // Apply gradient if needed
    if (showGradient) {
      final colors =
          gradientColors ??
          [
            calculatedBackgroundColor,
            calculatedBackgroundColor.withAlpha(179), // 0.7 * 255 = ~179
          ];

      avatarContent = Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: gradientBegin,
            end: gradientEnd,
            colors: colors,
          ),
          shape:
              shape == AvatarShape.circle
                  ? BoxShape.circle
                  : BoxShape.rectangle,
          borderRadius:
              shape == AvatarShape.rounded
                  ? BorderRadius.circular(size * 0.2)
                  : null,
        ),
        child: avatarContent,
      );
    }

    // Create the avatar container
    Widget avatar = Container(
      width: _getResponsiveAvatarWidth(context),
      height: _getResponsiveAvatarHeight(context),
      decoration: BoxDecoration(
        //color: showGradient ? null : calculatedBackgroundColor,
        color: const Color(0xFF0058FF),
        shape:
            shape == AvatarShape.circle ? BoxShape.circle : BoxShape.rectangle,
        borderRadius:
            shape == AvatarShape.rounded
                ? BorderRadius.circular(size * 0.2)
                : null,
        border:
            borderColor != null
                ? Border.all(color: borderColor!, width: borderWidth)
                : null,
        boxShadow:
            showShadow
                ? [
                  BoxShadow(
                    color: shadowColor,
                    blurRadius: shadowBlurRadius,
                    offset: shadowOffset,
                    spreadRadius: shadowElevation,
                  ),
                ]
                : null,
      ),
      child: avatarContent,
    );

    // Apply glow effect if needed
    if (showGlow) {
      avatar = Container(
        decoration: BoxDecoration(
          shape:
              shape == AvatarShape.circle
                  ? BoxShape.circle
                  : BoxShape.rectangle,
          borderRadius:
              shape == AvatarShape.rounded
                  ? BorderRadius.circular(size * 0.2)
                  : null,
          boxShadow: [
            BoxShadow(
              color: glowColor,
              blurRadius: glowRadius,
              spreadRadius: glowRadius / 2,
            ),
          ],
        ),
        child: avatar,
      );
    }

    // Apply material style if needed
    if (style == AvatarStyle.material) {
      avatar = Material(
        color: Colors.transparent,
        shape:
            shape == AvatarShape.circle
                ? const CircleBorder()
                : shape == AvatarShape.rounded
                ? RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(size * 0.2),
                )
                : const SquareBorder(),
        elevation: shadowElevation,
        shadowColor: shadowColor,
        child: avatar,
      );
    } else if (style == AvatarStyle.elevated) {
      avatar = PhysicalModel(
        color: Colors.transparent,
        elevation: shadowElevation,
        shadowColor: shadowColor,
        borderRadius:
            shape == AvatarShape.circle || shape == AvatarShape.rounded
                ? BorderRadius.circular(
                  shape == AvatarShape.circle ? size / 2 : size * 0.2,
                )
                : BorderRadius.zero,
        child: avatar,
      );
    }

    // Add badge if needed
    if (showBadge) {
      avatar = Stack(
        clipBehavior: Clip.none,
        children: [
          avatar,
          Positioned(
            top:
                badgePosition == BadgePosition.topRight ||
                        badgePosition == BadgePosition.topLeft
                    ? -badgeSize / 3
                    : null,
            bottom:
                badgePosition == BadgePosition.bottomRight ||
                        badgePosition == BadgePosition.bottomLeft
                    ? -badgeSize / 3
                    : null,
            right:
                badgePosition == BadgePosition.topRight ||
                        badgePosition == BadgePosition.bottomRight
                    ? -badgeSize / 3
                    : null,
            left:
                badgePosition == BadgePosition.topLeft ||
                        badgePosition == BadgePosition.bottomLeft
                    ? -badgeSize / 3
                    : null,
            child: Container(
              width: badgeSize,
              height: badgeSize,
              decoration: BoxDecoration(
                color: badgeColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: badgeBorderColor,
                  width: badgeBorderWidth,
                ),
              ),
              child:
                  badgeContent != null
                      ? Center(child: FittedBox(child: badgeContent))
                      : null,
            ),
          ),
        ],
      );
    }

    // Create a focus node if needed
    final FocusNode effectiveFocusNode = focusNode ?? FocusNode();

    // Add interactivity
    avatar = InkWell(
      onTap:
          onTap != null
              ? () {
                if (enableFeedback) {
                  HapticFeedback.selectionClick();
                }
                onTap!();
              }
              : null,
      onLongPress:
          onLongPress != null
              ? () {
                if (enableFeedback) {
                  HapticFeedback.heavyImpact();
                }
                onLongPress!();
              }
              : null,
      onDoubleTap:
          onDoubleTap != null
              ? () {
                if (enableFeedback) {
                  HapticFeedback.mediumImpact();
                }
                onDoubleTap!();
              }
              : null,
      borderRadius:
          shape == AvatarShape.circle
              ? BorderRadius.circular(size / 2)
              : shape == AvatarShape.rounded
              ? BorderRadius.circular(size * 0.2)
              : null,
      splashColor:
          showRipple
              ? (rippleColor ?? Theme.of(context).primaryColor.withAlpha(77))
              : Colors.transparent, // 0.3 * 255 = ~77
      hoverColor:
          showHover
              ? (hoverColor ?? Theme.of(context).primaryColor.withAlpha(26))
              : Colors.transparent, // 0.1 * 255 = ~26
      focusColor:
          showFocus
              ? (focusColor ?? Theme.of(context).primaryColor.withAlpha(51))
              : Colors.transparent, // 0.2 * 255 = ~51
      highlightColor:
          showPressed
              ? (pressedColor ?? Theme.of(context).primaryColor.withAlpha(51))
              : Colors.transparent, // 0.2 * 255 = ~51
      onHover: onHoverChanged,
      focusNode: effectiveFocusNode,
      autofocus: autofocus,
      child: avatar,
    );

    // Add focus listener
    if (onFocusChanged != null) {
      effectiveFocusNode.addListener(() {
        onFocusChanged!(effectiveFocusNode.hasFocus);
      });
    }

    // Add tooltip if needed
    if (showTooltip && tooltipMessage != null) {
      avatar = Tooltip(message: tooltipMessage!, child: avatar);
    }

    // Add semantics if needed
    if (semanticsLabel != null) {
      avatar = Semantics(label: semanticsLabel, child: avatar);
    }

    return avatar;
  }

  /// Generates the display text from the provided text or name.
  String _generateDisplayText() {
    String result = '';

    if (text != null && text!.isNotEmpty) {
      // Use provided text
      result = text!;
    } else if (name != null && name!.isNotEmpty) {
      // Generate initials from name
      final nameParts = name!.trim().split(' ');
      if (nameParts.length == 1) {
        // Single name, take first character
        result = nameParts[0][0];
      } else {
        // Multiple names, take first character of first and last name
        result = nameParts[0][0] + nameParts[nameParts.length - 1][0];
      }
    } else {
      // Default to a question mark if no text or name provided
      result = '?';
    }

    // Apply uppercase if needed
    if (uppercase) {
      result = result.toUpperCase();
    }

    // Limit to max characters
    if (result.length > maxChars) {
      result = result.substring(0, maxChars);
    }

    return result;
  }

  /// Generates a color based on the input text.
  Color _generateColorFromText(String text) {
    if (text.isEmpty) return Colors.blue;

    // Use a hash of the text to generate a consistent color
    final int hash = text.codeUnits.fold(0, (prev, element) => prev + element);
    final List<Color> colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
    ];

    return colors[hash % colors.length];
  }
}

/// The shape of the avatar.
enum AvatarShape {
  /// A circular avatar.
  circle,

  /// A square avatar.
  square,

  /// A rounded square avatar.
  rounded,
}

/// The position of the badge.
enum BadgePosition {
  /// Top right position.
  topRight,

  /// Top left position.
  topLeft,

  /// Bottom right position.
  bottomRight,

  /// Bottom left position.
  bottomLeft,
}

/// The style of the avatar.
enum AvatarStyle {
  /// A flat avatar with no elevation.
  flat,

  /// A material avatar with elevation.
  material,

  /// An elevated avatar with a physical model.
  elevated,
}

/// A square border shape.
class SquareBorder extends ShapeBorder {
  const SquareBorder();

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.zero;

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()..addRect(rect);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return Path()..addRect(rect);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    // No painting needed
  }

  @override
  ShapeBorder scale(double t) {
    return this;
  }
}

double _getResponsiveAvatarWidth(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 48.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 40.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveAvatarHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 48.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 40.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
