import 'package:dio/dio.dart';

class JavaFileService {
  static const String _baseUrl = 'http://10.26.1.52:8084/api/java-files/read';
  late final Dio _dio;

  JavaFileService() {
    _dio = Dio();
    _dio.options.headers['Content-Type'] = 'application/json';
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  /// Fetches Java file content from the API
  Future<JavaFileResponse?> fetchJavaFile(String filePath) async {
    try {
      print('Fetching Java file from: $_baseUrl');
      print('File path parameter: $filePath');
      
      final response = await _dio.get(
        _baseUrl,
        queryParameters: {
          'goId': filePath,
        },
      );

      print('Response status code: ${response.statusCode}');
      print('Response data type: ${response.data.runtimeType}');
      print('Response data: ${response.data}');

      if (response.statusCode == 200) {
        // Handle different response formats
        if (response.data is String) {
          // If response is directly a string (the file content)
          return JavaFileResponse(
            content: response.data,
            fileName: response.data["fileName"],
            filePath: response.data["fileName"],
            success: true,
            message: 'File loaded successfully',
          );
        } else if (response.data is Map<String, dynamic>) {
          // Check if it's the nested API format: {"solution": {"java": {"lines": [...]}}}
          if (response.data.containsKey('solution') && 
              response.data['solution'] is Map<String, dynamic> &&
              response.data['solution']['java'] is Map<String, dynamic> &&
              response.data['solution']['java']['lines'] is List) {
            
            print('Found nested API format with lines array');
            List<dynamic> lines = response.data['solution']['java']['lines'];
            
            // Convert lines array to content string
            StringBuffer contentBuffer = StringBuffer();
            for (var line in lines) {
              if (line is Map<String, dynamic> && line.containsKey('content')) {
                contentBuffer.writeln(line['content']);
              }
            }
            
            String content = contentBuffer.toString();
            print('Converted ${lines.length} lines to content string of length ${content.length}');
            
            return JavaFileResponse(
              content: content,
              fileName: response.data["fileName"] ,
              filePath: response.data["fileName"],
              success: true,
              message: 'File loaded successfully from nested format',
            );
          } else {
            // Try standard JSON format
            return JavaFileResponse.fromJson(response.data);
          }
        } else {
          print('Unexpected response format: ${response.data}');
          return null;
        }
      } else {
        print('Failed to load Java file: ${response.statusCode}');
        print('Response data: ${response.data}');
        return null;
      }
    } on DioException catch (e) {
      print('Dio error fetching Java file: ${e.message}');
      print('Error type: ${e.type}');
      if (e.response != null) {
        print('Response status: ${e.response?.statusCode}');
        print('Response data: ${e.response?.data}');
      }
      return null;
    } catch (e) {
      print('Error fetching Java file: $e');
      return null;
    }
  }

  /// Processes the Java file content into lines with line numbers
  List<JavaCodeLine> processJavaContent(String content) {
    List<String> lines = content.split('\n');
    List<JavaCodeLine> codeLines = [];

    for (int i = 0; i < lines.length; i++) {
      codeLines.add(JavaCodeLine(
        lineNumber: i + 1,
        content: lines[i],
      ));
    }

    return codeLines;
  }
}

class JavaFileResponse {
  final String? content;
  final String? fileName;
  final String? filePath;
  final bool? success;
  final String? message;

  JavaFileResponse({
    this.content,
    this.fileName,
    this.filePath,
    this.success,
    this.message,
  });

  factory JavaFileResponse.fromJson(Map<String, dynamic> json) {
    return JavaFileResponse(
      content: json['content'],
      fileName: json['fileName'],
      filePath: json['filePath'],
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'fileName': fileName,
      'filePath': filePath,
      'success': success,
      'message': message,
    };
  }
}

class JavaCodeLine {
  final int lineNumber;
  final String content;

  JavaCodeLine({
    required this.lineNumber,
    required this.content,
  });
}
