import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A customizable time input/selection widget.
///
/// This widget provides a time picker with various customization options
/// including format, appearance, and behavior.
class TimeWidget extends StatefulWidget {
  /// Initial time value
  final TimeOfDay initialTime;

  /// Format of the time display
  final TimeFormat format;

  /// Whether to use 24-hour format (military time)
  final bool use24HourFormat;

  /// Whether to show seconds
  final bool showSeconds;

  /// Whether to show AM/PM indicator
  final bool showAmPm;

  /// Whether to allow time selection
  final bool allowSelection;

  /// Whether to show a time picker dialog or inline picker
  final bool useDialog;

  /// Whether to show a clear button
  final bool showClearButton;

  /// Size of the time text
  final double fontSize;

  /// Font weight of the time text
  final FontWeight fontWeight;

  /// Font family of the time text
  final String? fontFamily;

  /// Color of the time text
  final Color textColor;

  /// Background color of the widget
  final Color backgroundColor;

  /// Border color of the widget
  final Color? borderColor;

  /// Border width of the widget
  final double borderWidth;

  /// Border radius of the widget
  final double borderRadius;

  /// Width of the widget
  final double? width;

  /// Height of the widget
  final double? height;

  /// Padding around the time text
  final EdgeInsetsGeometry padding;

  /// Alignment of the time within its container
  final Alignment alignment;

  /// Whether to show a separator between hours, minutes, and seconds
  final bool showSeparator;

  /// Separator character (e.g., ":", "-", ".")
  final String separator;

  /// Whether to show a digital clock style
  final bool digitalStyle;

  /// Whether to show a shadow under the text
  final bool showShadow;

  /// Shadow color
  final Color shadowColor;

  /// Shadow offset
  final Offset shadowOffset;

  /// Shadow blur radius
  final double shadowBlurRadius;

  /// Whether to show hour spinner
  final bool showHourSpinner;

  /// Whether to show minute spinner
  final bool showMinuteSpinner;

  /// Whether to show second spinner
  final bool showSecondSpinner;

  /// Whether to show AM/PM spinner
  final bool showAmPmSpinner;

  /// Color of the spinner
  final Color spinnerColor;

  /// Background color of the spinner
  final Color spinnerBackgroundColor;

  /// Icon for the time picker
  final IconData pickerIcon;

  /// Color of the picker icon
  final Color pickerIconColor;

  /// Label text for the time input
  final String? labelText;

  /// Helper text for the time input
  final String? helperText;

  /// Error text for the time input
  final String? errorText;

  /// Whether the input is required
  final bool required;

  /// Whether the input is enabled
  final bool enabled;

  /// Callback when the time changes
  final ValueChanged<TimeOfDay>? onChanged;

  /// Callback when the time is selected
  final ValueChanged<TimeOfDay>? onSelected;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  const TimeWidget({
    super.key,
    this.initialTime = const TimeOfDay(hour: 12, minute: 0),
    this.format = TimeFormat.standard,
    this.use24HourFormat = false,
    this.showSeconds = false,
    this.showAmPm = true,
    this.allowSelection = true,
    this.useDialog = true,
    this.showClearButton = true,
    this.fontSize = 16.0,
    this.fontWeight = FontManager.medium,
    this.fontFamily = FontManager.fontFamilyInter,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.alignment = Alignment.center,
    this.showSeparator = true,
    this.separator = ":",
    this.digitalStyle = false,
    this.showShadow = false,
    this.shadowColor = Colors.black26,
    this.shadowOffset = const Offset(2, 2),
    this.shadowBlurRadius = 4.0,
    this.showHourSpinner = false,
    this.showMinuteSpinner = false,
    this.showSecondSpinner = false,
    this.showAmPmSpinner = false,
    this.spinnerColor = Colors.blue,
    this.spinnerBackgroundColor = Colors.grey,
    this.pickerIcon = Icons.access_time,
    this.pickerIconColor = Colors.blue,
    this.labelText,
    this.helperText,
    this.errorText,
    this.required = false,
    this.enabled = true,
    this.onChanged,
    this.onSelected,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  /// Creates a TimeWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the TimeWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialTime": "14:30",
  ///   "format": "standard",
  ///   "use24HourFormat": true,
  ///   "textColor": "white",
  ///   "backgroundColor": "black",
  ///   "digitalStyle": true
  /// }
  /// ```
  factory TimeWidget.fromJson(Map<String, dynamic> json) {
    // Parse initial time
    TimeOfDay parseTimeOfDay(dynamic timeValue) {
      if (timeValue == null) return const TimeOfDay(hour: 12, minute: 0);

      if (timeValue is String) {
        // Try to parse "HH:MM" format
        final parts = timeValue.split(':');
        if (parts.length >= 2) {
          final hour = int.tryParse(parts[0]) ?? 12;
          final minute = int.tryParse(parts[1]) ?? 0;
          return TimeOfDay(
            hour: hour.clamp(0, 23),
            minute: minute.clamp(0, 59),
          );
        }
      } else if (timeValue is Map<String, dynamic>) {
        final hour = (timeValue['hour'] as int?) ?? 12;
        final minute = (timeValue['minute'] as int?) ?? 0;
        return TimeOfDay(hour: hour.clamp(0, 23), minute: minute.clamp(0, 59));
      }

      return const TimeOfDay(hour: 12, minute: 0);
    }

    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          case 'teal':
            return Colors.teal;
          case 'cyan':
            return Colors.cyan;
          case 'amber':
            return Colors.amber;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse(
                  '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
                );
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          case 'thin':
            return FontWeight.w200;
          case 'medium':
            return FontWeight.w500;
          case 'semibold':
            return FontWeight.w600;
          case 'extrabold':
            return FontWeight.w800;
          case 'black':
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      }

      return FontWeight.normal;
    }

    // Parse alignment
    Alignment parseAlignment(dynamic alignmentValue) {
      if (alignmentValue == null) return Alignment.center;

      if (alignmentValue is String) {
        switch (alignmentValue.toLowerCase()) {
          case 'center':
            return Alignment.center;
          case 'topleft':
          case 'top_left':
            return Alignment.topLeft;
          case 'topright':
          case 'top_right':
            return Alignment.topRight;
          case 'bottomleft':
          case 'bottom_left':
            return Alignment.bottomLeft;
          case 'bottomright':
          case 'bottom_right':
            return Alignment.bottomRight;
          case 'top':
          case 'topcenter':
          case 'top_center':
            return Alignment.topCenter;
          case 'bottom':
          case 'bottomcenter':
          case 'bottom_center':
            return Alignment.bottomCenter;
          case 'left':
          case 'centerleft':
          case 'center_left':
            return Alignment.centerLeft;
          case 'right':
          case 'centerright':
          case 'center_right':
            return Alignment.centerRight;
          default:
            return Alignment.center;
        }
      }

      return Alignment.center;
    }

    // Parse offset
    Offset parseOffset(dynamic offsetValue) {
      if (offsetValue == null) return const Offset(2, 2);

      if (offsetValue is Map<String, dynamic>) {
        final dx = (offsetValue['dx'] as num?)?.toDouble() ?? 2.0;
        final dy = (offsetValue['dy'] as num?)?.toDouble() ?? 2.0;
        return Offset(dx, dy);
      } else if (offsetValue is List && offsetValue.length >= 2) {
        final dx = (offsetValue[0] as num?)?.toDouble() ?? 2.0;
        final dy = (offsetValue[1] as num?)?.toDouble() ?? 2.0;
        return Offset(dx, dy);
      }

      return const Offset(2, 2);
    }

    // Parse time format
    TimeFormat parseTimeFormat(dynamic formatValue) {
      if (formatValue == null) return TimeFormat.standard;

      if (formatValue is String) {
        switch (formatValue.toLowerCase()) {
          case 'standard':
            return TimeFormat.standard;
          case 'short':
            return TimeFormat.short;
          case 'long':
            return TimeFormat.long;
          case 'compact':
            return TimeFormat.compact;
          case 'custom':
            return TimeFormat.custom;
          default:
            return TimeFormat.standard;
        }
      } else if (formatValue is int) {
        switch (formatValue) {
          case 0:
            return TimeFormat.standard;
          case 1:
            return TimeFormat.short;
          case 2:
            return TimeFormat.long;
          case 3:
            return TimeFormat.compact;
          case 4:
            return TimeFormat.custom;
          default:
            return TimeFormat.standard;
        }
      }

      return TimeFormat.standard;
    }

    // Parse padding
    EdgeInsetsGeometry parsePadding(dynamic paddingValue) {
      if (paddingValue == null) return const EdgeInsets.all(16.0);

      if (paddingValue is num) {
        return EdgeInsets.all(paddingValue.toDouble());
      } else if (paddingValue is Map<String, dynamic>) {
        final left = (paddingValue['left'] as num?)?.toDouble() ?? 0.0;
        final top = (paddingValue['top'] as num?)?.toDouble() ?? 0.0;
        final right = (paddingValue['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (left == right && top == bottom && left == top) {
          return EdgeInsets.all(left);
        } else if (left == right && top == bottom) {
          return EdgeInsets.symmetric(horizontal: left, vertical: top);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (paddingValue is String) {
        switch (paddingValue.toLowerCase()) {
          case 'none':
          case 'zero':
            return EdgeInsets.zero;
          case 'small':
            return const EdgeInsets.all(8.0);
          case 'medium':
            return const EdgeInsets.all(16.0);
          case 'large':
            return const EdgeInsets.all(24.0);
          default:
            return const EdgeInsets.all(16.0);
        }
      }

      return const EdgeInsets.all(16.0);
    }

    // Parse icon data
    IconData parseIconData(dynamic iconValue, IconData defaultIcon) {
      if (iconValue == null) return defaultIcon;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'access_time':
            return Icons.access_time;
          case 'alarm':
            return Icons.alarm;
          case 'timer':
            return Icons.timer;
          case 'schedule':
            return Icons.schedule;
          case 'clock':
            return Icons.watch_later;
          case 'calendar':
            return Icons.calendar_today;
          case 'date':
            return Icons.date_range;
          case 'clear':
            return Icons.clear;
          case 'delete':
            return Icons.delete;
          case 'remove':
            return Icons.remove;
          case 'add':
            return Icons.add;
          case 'edit':
            return Icons.edit;
          case 'save':
            return Icons.save;
          case 'check':
            return Icons.check;
          case 'close':
            return Icons.close;
          case 'settings':
            return Icons.settings;
          case 'info':
            return Icons.info;
          case 'warning':
            return Icons.warning;
          case 'error':
            return Icons.error;
          case 'help':
            return Icons.help;
          default:
            return defaultIcon;
        }
      }

      return defaultIcon;
    }

    return TimeWidget(
      initialTime: parseTimeOfDay(json['initialTime']),
      format: parseTimeFormat(json['format']),
      use24HourFormat: json['use24HourFormat'] as bool? ?? false,
      showSeconds: json['showSeconds'] as bool? ?? false,
      showAmPm: json['showAmPm'] as bool? ?? true,
      allowSelection: json['allowSelection'] as bool? ?? true,
      useDialog: json['useDialog'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? true,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      fontFamily: json['fontFamily'] as String?,
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']) ?? Color(0xFFCCCCCC),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      padding: parsePadding(json['padding']),
      alignment: parseAlignment(json['alignment']),
      showSeparator: json['showSeparator'] as bool? ?? true,
      separator: json['separator'] as String? ?? ":",
      digitalStyle: json['digitalStyle'] as bool? ?? false,
      showShadow: json['showShadow'] as bool? ?? false,
      shadowColor: parseColor(json['shadowColor']) ?? Colors.black26,
      shadowOffset: parseOffset(json['shadowOffset']),
      shadowBlurRadius: (json['shadowBlurRadius'] as num?)?.toDouble() ?? 4.0,
      showHourSpinner: json['showHourSpinner'] as bool? ?? false,
      showMinuteSpinner: json['showMinuteSpinner'] as bool? ?? false,
      showSecondSpinner: json['showSecondSpinner'] as bool? ?? false,
      showAmPmSpinner: json['showAmPmSpinner'] as bool? ?? false,
      spinnerColor: parseColor(json['spinnerColor']) ?? Colors.blue,
      spinnerBackgroundColor:
          parseColor(json['spinnerBackgroundColor']) ?? Colors.grey,
      pickerIcon: parseIconData(json['pickerIcon'], Icons.access_time),
      pickerIconColor: parseColor(json['pickerIconColor']) ?? Colors.blue,
      labelText: json['labelText'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      required: json['required'] as bool? ?? false,
      enabled: json['enabled'] as bool? ?? true,
      onChanged:
          json['onChanged'] == true
              ? (time) {
                debugPrint('Time changed: $time');
              }
              : null,
      onSelected:
          json['onSelected'] == true
              ? (time) {
                debugPrint('Time selected: $time');
              }
              : null,
      onHover:
          json['onHover'] == true
              ? (isHovered) {
                debugPrint('Time widget hover: $isHovered');
              }
              : null,
      onFocus:
          json['onFocus'] == true
              ? (isFocused) {
                debugPrint('Time widget focus: $isFocused');
              }
              : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']) ?? Color(0xFF0058FF),
      focusColor: parseColor(json['focusColor']),
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onTap:
          json['onTap'] == true
              ? () {
                debugPrint('Time widget tapped');
              }
              : null,
      onDoubleTap:
          json['onDoubleTap'] == true
              ? () {
                debugPrint('Time widget double-tapped');
              }
              : null,
      onLongPress:
          json['onLongPress'] == true
              ? () {
                debugPrint('Time widget long-pressed');
              }
              : null,
    );
  }

  /// Converts the TimeWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'initialTime':
          '${initialTime.hour.toString().padLeft(2, '0')}:${initialTime.minute.toString().padLeft(2, '0')}',
      'format': _formatToString(format),
      'use24HourFormat': use24HourFormat,
      'showSeconds': showSeconds,
      'showAmPm': showAmPm,
      'allowSelection': allowSelection,
      'useDialog': useDialog,
      'showClearButton': showClearButton,
      'fontSize': fontSize,
      'fontWeight': _fontWeightToString(fontWeight),
      'textColor': _colorToString(textColor),
      'backgroundColor': _colorToString(backgroundColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'showSeparator': showSeparator,
      'separator': separator,
      'digitalStyle': digitalStyle,
      'showShadow': showShadow,
      'shadowColor': _colorToString(shadowColor),
      'shadowBlurRadius': shadowBlurRadius,
      'showHourSpinner': showHourSpinner,
      'showMinuteSpinner': showMinuteSpinner,
      'showSecondSpinner': showSecondSpinner,
      'showAmPmSpinner': showAmPmSpinner,
      'spinnerColor': _colorToString(spinnerColor),
      'spinnerBackgroundColor': _colorToString(spinnerBackgroundColor),
      'pickerIcon': _iconDataToString(pickerIcon),
      'pickerIconColor': _colorToString(pickerIconColor),
      'required': required,
      'enabled': enabled,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties
    if (fontFamily != null) json['fontFamily'] = fontFamily;
    if (borderColor != null) json['borderColor'] = _colorToString(borderColor!);
    if (width != null) json['width'] = width;
    if (height != null) json['height'] = height;
    if (labelText != null) json['labelText'] = labelText;
    if (helperText != null) json['helperText'] = helperText;
    if (errorText != null) json['errorText'] = errorText;
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;

    // Add shadow offset
    json['shadowOffset'] = {'dx': shadowOffset.dx, 'dy': shadowOffset.dy};

    // Add padding
    if (padding is EdgeInsets) {
      final EdgeInsets p = padding as EdgeInsets;
      if (p.left == p.right && p.top == p.bottom && p.left == p.top) {
        json['padding'] = p.left;
      } else {
        json['padding'] = {
          'left': p.left,
          'top': p.top,
          'right': p.right,
          'bottom': p.bottom,
        };
      }
    }

    // Add alignment
    json['alignment'] = _alignmentToString(alignment);

    // Add callback flags
    if (onChanged != null) json['onChanged'] = true;
    if (onSelected != null) json['onSelected'] = true;
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;
    if (onTap != null) json['onTap'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';
    if (color == Colors.teal) return 'teal';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Helper method to convert a FontWeight to a string
  static String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.bold) return 'bold';
    if (weight == FontWeight.normal) return 'normal';
    if (weight == FontWeight.w100) return '100';
    if (weight == FontWeight.w200) return '200';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w400) return 'normal';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w700) return 'bold';
    if (weight == FontWeight.w800) return 'extrabold';
    if (weight == FontWeight.w900) return 'black';

    return 'normal';
  }

  /// Helper method to convert a TimeFormat to a string
  static String _formatToString(TimeFormat format) {
    switch (format) {
      case TimeFormat.standard:
        return 'standard';
      case TimeFormat.short:
        return 'short';
      case TimeFormat.long:
        return 'long';
      case TimeFormat.compact:
        return 'compact';
      case TimeFormat.custom:
        return 'custom';
    }
  }

  /// Helper method to convert an Alignment to a string
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.topLeft) return 'topLeft';
    if (alignment == Alignment.topCenter) return 'top';
    if (alignment == Alignment.topRight) return 'topRight';
    if (alignment == Alignment.centerLeft) return 'left';
    if (alignment == Alignment.centerRight) return 'right';
    if (alignment == Alignment.bottomLeft) return 'bottomLeft';
    if (alignment == Alignment.bottomCenter) return 'bottom';
    if (alignment == Alignment.bottomRight) return 'bottomRight';

    return 'center';
  }

  /// Helper method to convert an IconData to a string
  static String _iconDataToString(IconData icon) {
    if (icon == Icons.access_time) return 'access_time';
    if (icon == Icons.alarm) return 'alarm';
    if (icon == Icons.timer) return 'timer';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.watch_later) return 'clock';
    if (icon == Icons.calendar_today) return 'calendar';
    if (icon == Icons.date_range) return 'date';
    if (icon == Icons.clear) return 'clear';
    if (icon == Icons.delete) return 'delete';
    if (icon == Icons.remove) return 'remove';
    if (icon == Icons.add) return 'add';
    if (icon == Icons.edit) return 'edit';
    if (icon == Icons.save) return 'save';
    if (icon == Icons.check) return 'check';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.info) return 'info';
    if (icon == Icons.warning) return 'warning';
    if (icon == Icons.error) return 'error';
    if (icon == Icons.help) return 'help';

    return 'access_time';
  }

  @override
  State<TimeWidget> createState() => _TimeWidgetState();
}

/// Format options for the time display
enum TimeFormat {
  /// Standard format (e.g., "3:45 PM" or "15:45")
  standard,

  /// Short format (e.g., "3:45" or "15:45")
  short,

  /// Long format (e.g., "3 hours 45 minutes")
  long,

  /// Compact format (e.g., "3h 45m")
  compact,

  /// Custom format using DateFormat pattern
  custom,
}

class _TimeWidgetState extends State<TimeWidget> {
  late TimeOfDay _selectedTime;
  int _seconds = 0;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _selectedTime = widget.initialTime;

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  void dispose() {
    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    } else {
      // Just remove our listener
      _focusNode.removeListener(_handleFocusChange);
    }

    super.dispose();
  }

  String _formatTime() {
    final hour = _selectedTime.hour;
    final minute = _selectedTime.minute;

    // Adjust hour for 12-hour format if needed
    final displayHour =
        widget.use24HourFormat ? hour : _selectedTime.hourOfPeriod;

    // Determine AM/PM
    final amPm = hour < 12 ? 'AM' : 'PM';

    // Format based on the selected format
    switch (widget.format) {
      case TimeFormat.standard:
        final hourStr =
            displayHour == 0 && !widget.use24HourFormat
                ? '12'
                : displayHour.toString();
        final minuteStr = minute.toString().padLeft(2, '0');
        final secondStr = _seconds.toString().padLeft(2, '0');

        final separator = widget.showSeparator ? widget.separator : ' ';

        String timeStr = '';
        if (widget.showSeconds) {
          timeStr = '$hourStr$separator$minuteStr$separator$secondStr';
        } else {
          timeStr = '$hourStr$separator$minuteStr';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        return timeStr;

      case TimeFormat.short:
        final hourStr =
            displayHour == 0 && !widget.use24HourFormat
                ? '12'
                : displayHour.toString();
        final minuteStr = minute.toString().padLeft(2, '0');

        final separator = widget.showSeparator ? widget.separator : ' ';

        String timeStr = '$hourStr$separator$minuteStr';

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        return timeStr;

      case TimeFormat.long:
        final hourStr = '$displayHour ${displayHour == 1 ? 'hour' : 'hours'}';
        final minuteStr = '$minute ${minute == 1 ? 'minute' : 'minutes'}';

        String timeStr = '$hourStr $minuteStr';

        if (widget.showSeconds) {
          final secondStr = '$_seconds ${_seconds == 1 ? 'second' : 'seconds'}';
          timeStr += ' $secondStr';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        return timeStr;

      case TimeFormat.compact:
        final hourStr = '${displayHour}h';
        final minuteStr = '${minute}m';

        String timeStr = '$hourStr $minuteStr';

        if (widget.showSeconds) {
          final secondStr = '${_seconds}s';
          timeStr += ' $secondStr';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        return timeStr;

      case TimeFormat.custom:
        // Create a DateTime to use with DateFormat
        final now = DateTime.now();
        final dateTime = DateTime(
          now.year,
          now.month,
          now.day,
          _selectedTime.hour,
          _selectedTime.minute,
          _seconds,
        );

        // Use IntlDateFormat for custom formatting
        String pattern = widget.use24HourFormat ? 'HH:mm' : 'h:mm';

        if (widget.showSeconds) {
          pattern += ':ss';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          pattern += ' a';
        }

        return DateFormat(pattern).format(dateTime);
    }
  }

  Future<void> _selectTime() async {
    if (!widget.allowSelection || !widget.enabled) return;

    if (widget.useDialog) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: _selectedTime,

        // builder: (BuildContext context, Widget? child) {
        //   return MediaQuery(
        //     data: MediaQuery.of(context).copyWith(
        //       alwaysUse24HourFormat: widget.use24HourFormat,
        //     ),
        //     child: child!,
        //   );
        // },
        builder: (BuildContext context, Widget? child) {
          return Theme(
            data: ThemeData(
              colorScheme: ColorScheme.light(
                primary: const Color(0xFF0058FF),
                onBackground: const Color(0xFFCCCCCC),
                secondary: const Color(0xFF0058FF),
                onSecondary: Colors.white,
              ),
            ),
            child: MediaQuery(
              data: MediaQuery.of(
                context,
              ).copyWith(alwaysUse24HourFormat: widget.use24HourFormat),
              child: child ?? const SizedBox(),
            ),
          );
        },
      );

      if (pickedTime != null && pickedTime != _selectedTime) {
        setState(() {
          _selectedTime = pickedTime;
        });

        if (widget.onChanged != null) {
          widget.onChanged!(_selectedTime);
        }

        if (widget.onSelected != null) {
          widget.onSelected!(_selectedTime);
        }
      }
    }
  }

  void _clearTime() {
    setState(() {
      _selectedTime = const TimeOfDay(hour: 0, minute: 0);
      _seconds = 0;
    });

    if (widget.onChanged != null) {
      widget.onChanged!(_selectedTime);
    }
  }

  Widget _buildSpinners() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showHourSpinner)
          _buildSpinner(
            value:
                widget.use24HourFormat
                    ? _selectedTime.hour
                    : _selectedTime.hourOfPeriod,
            minValue: widget.use24HourFormat ? 0 : 1,
            maxValue: widget.use24HourFormat ? 23 : 12,
            onChanged: (value) {
              setState(() {
                if (widget.use24HourFormat) {
                  _selectedTime = TimeOfDay(
                    hour: value,
                    minute: _selectedTime.minute,
                  );
                } else {
                  final isPm = _selectedTime.hour >= 12;
                  final newHour = isPm ? value + 12 : value;
                  _selectedTime = TimeOfDay(
                    hour: newHour % 24,
                    minute: _selectedTime.minute,
                  );
                }
              });

              if (widget.onChanged != null) {
                widget.onChanged!(_selectedTime);
              }
            },
          ),

        if (widget.showHourSpinner && widget.showMinuteSpinner)
          Text(
            widget.separator,
            // style: TextStyle(
            //   fontSize: widget.fontSize,
            //   fontWeight: widget.fontWeight,
            //   color: widget.textColor,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: widget.textColor,
              fontSize: _getResponsiveFontSize(context),
            ),
          ),

        if (widget.showMinuteSpinner)
          _buildSpinner(
            value: _selectedTime.minute,
            minValue: 0,
            maxValue: 59,
            onChanged: (value) {
              setState(() {
                _selectedTime = TimeOfDay(
                  hour: _selectedTime.hour,
                  minute: value,
                );
              });

              if (widget.onChanged != null) {
                widget.onChanged!(_selectedTime);
              }
            },
          ),

        if (widget.showMinuteSpinner &&
            widget.showSecondSpinner &&
            widget.showSeconds)
          Text(
            widget.separator,

            // style: TextStyle(
            //   fontSize: widget.fontSize,
            //   fontWeight: widget.fontWeight,
            //   color: widget.textColor,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: widget.textColor,
              fontSize: _getResponsiveFontSize(context),
            ),
          ),

        if (widget.showSecondSpinner && widget.showSeconds)
          _buildSpinner(
            value: _seconds,
            minValue: 0,
            maxValue: 59,
            onChanged: (value) {
              setState(() {
                _seconds = value;
              });

              if (widget.onChanged != null) {
                widget.onChanged!(_selectedTime);
              }
            },
          ),

        if (widget.showAmPmSpinner &&
            !widget.use24HourFormat &&
            widget.showAmPm)
          Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: _buildAmPmSpinner(),
          ),
      ],
    );
  }

  Widget _buildSpinner({
    required int value,
    required int minValue,
    required int maxValue,
    required ValueChanged<int> onChanged,
  }) {
    return Container(
      width: 60,
      height: 100,
      decoration: BoxDecoration(
        color: widget.spinnerBackgroundColor.withAlpha(50),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_drop_up),
            color: widget.spinnerColor,
            onPressed: () {
              final newValue = value + 1 > maxValue ? minValue : value + 1;
              onChanged(newValue);
            },
          ),
          Text(
            value.toString().padLeft(2, '0'),
            style: TextStyle(
              fontSize: _getResponsiveFontSize(context),
              fontWeight: widget.fontWeight,
              color: widget.textColor,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_drop_down),
            color: widget.spinnerColor,
            onPressed: () {
              final newValue = value - 1 < minValue ? maxValue : value - 1;
              onChanged(newValue);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAmPmSpinner() {
    final isPm = _selectedTime.hour >= 12;

    return Container(
      width: 60,
      height: 100,
      decoration: BoxDecoration(
        color: widget.spinnerBackgroundColor.withAlpha(50),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_drop_up),
            color: widget.spinnerColor,
            onPressed: () {
              setState(() {
                if (isPm) {
                  // Switch to AM
                  _selectedTime = TimeOfDay(
                    hour: _selectedTime.hour - 12,
                    minute: _selectedTime.minute,
                  );
                } else {
                  // Switch to PM
                  _selectedTime = TimeOfDay(
                    hour: _selectedTime.hour + 12,
                    minute: _selectedTime.minute,
                  );
                }
              });

              if (widget.onChanged != null) {
                widget.onChanged!(_selectedTime);
              }
            },
          ),
          Text(
            isPm ? 'PM' : 'AM',

            // style: TextStyle(
            //   fontSize: widget.fontSize,
            //   fontWeight: widget.fontWeight,
            //   color: widget.textColor,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: widget.textColor,
              fontSize: _getResponsiveFontSize(context),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_drop_down),
            color: widget.spinnerColor,
            onPressed: () {
              setState(() {
                if (isPm) {
                  // Switch to AM
                  _selectedTime = TimeOfDay(
                    hour: _selectedTime.hour - 12,
                    minute: _selectedTime.minute,
                  );
                } else {
                  // Switch to PM
                  _selectedTime = TimeOfDay(
                    hour: _selectedTime.hour + 12,
                    minute: _selectedTime.minute,
                  );
                }
              });

              if (widget.onChanged != null) {
                widget.onChanged!(_selectedTime);
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final timeText = _formatTime();

    // Create text style with or without shadow
    final textStyle = TextStyle(
      fontSize: _getResponsiveFontSize(context),
      fontWeight: widget.fontWeight,
      fontFamily: widget.fontFamily,
      color:
          _isFocused && widget.focusColor != null
              ? widget.focusColor!
              : _isHovered && widget.hoverColor != null
              ? widget.hoverColor!
              : widget.textColor,
      shadows:
          widget.showShadow
              ? [
                Shadow(
                  color: widget.shadowColor,
                  offset: widget.shadowOffset,
                  blurRadius: widget.shadowBlurRadius,
                ),
              ]
              : null,
    );

    // Digital clock style uses a monospace font
    final digitalTextStyle =
        widget.digitalStyle
            ? textStyle.copyWith(
              fontFamily: widget.fontFamily,
              fontWeight: widget.fontWeight,
              letterSpacing: 2.0,
            )
            : textStyle;

    // Build the main content based on whether spinners are shown
    Widget content;

    if (widget.showHourSpinner ||
        widget.showMinuteSpinner ||
        widget.showSecondSpinner ||
        widget.showAmPmSpinner) {
      // Show spinners
      content = _buildSpinners();
    } else {
      // Show text with optional picker
      content = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            timeText,
            // style: TextStyle(
            //   fontSize: _getResponsiveInputFontSize(context),
            //   fontWeight: widget.fontWeight,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              fontSize: _getResponsiveValueFontSize(context),
              color: const Color(0xFF333333),
            ),
          ),
          if (widget.allowSelection && widget.enabled)
            GestureDetector(
              onTap: _selectTime,
              // child: Icon(
              //   widget.pickerIcon,
              //   color: _isHovered ? Color(0xFF0058FF) : Color(0xFFCCCCCC),
              //   size: 20.0,
              // ),
              // SvgPicture.asset(
              //   'packages/ui_controls_library/assets/images/time.svg',
              //   width: _getResponsiveIconSize(context),
              // ),
              child: SvgPicture.asset(
                _isHovered
                    ? 'assets/images/cu-clock-hover.svg'
                    : 'assets/images/cu-clock.svg',
                package: 'ui_controls_library',
                // width: _getResponsiveIconSize(context),
              ),
            ),
        ],
      );
    }

    // Build the base container
    Widget containerWidget = Container(
      width: widget.width,
      height: _getResponsiveHeight(context),
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: _isHovered ? const Color(0xFF0058FF) : const Color(0xFFCCCCCC),
          width: widget.borderWidth,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (widget.labelText != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                widget.required ? "${widget.labelText}*" : widget.labelText!,

                // style: TextStyle(
                //   fontSize: 14,
                //   color:
                //       _isFocused && widget.focusColor != null
                //           ? widget.focusColor!
                //           : _isHovered && widget.hoverColor != null
                //           ? widget.hoverColor!
                //           : widget.enabled
                //           ? Colors.black87
                //           : Colors.black38,
                // ),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  color:
                      _isFocused && widget.focusColor != null
                          ? widget.focusColor!
                          : _isHovered && widget.hoverColor != null
                          ? widget.hoverColor!
                          : widget.enabled
                          ? Colors.black87
                          : Colors.black38,
                  fontSize: _getResponsiveFontSize(context),
                ),
              ),
            ),
          Align(alignment: widget.alignment, child: content),
          if (widget.helperText != null || widget.errorText != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                widget.errorText ?? widget.helperText!,
                // style: TextStyle(
                //   fontSize: 12,
                //   color: widget.errorText != null ? Colors.red : Colors.black54,
                // ),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  color: widget.errorText != null ? Colors.red : Colors.black54,
                  fontSize: _getResponsiveFontSize(context),
                ),
              ),
            ),
        ],
      ),
    );

    // Add gesture detector for advanced interactions
    Widget gestureWidget = GestureDetector(
      onTap: () {
        if (widget.allowSelection && widget.enabled) {
          if (widget.enableFeedback) {
            HapticFeedback.selectionClick();
          }
          _selectTime();
        }
        if (widget.onTap != null) {
          widget.onTap!();
        }
      },
      onDoubleTap:
          widget.onDoubleTap != null
              ? () {
                if (widget.enableFeedback) {
                  HapticFeedback.selectionClick();
                }
                widget.onDoubleTap!();
              }
              : null,
      onLongPress:
          widget.onLongPress != null
              ? () {
                if (widget.enableFeedback) {
                  HapticFeedback.heavyImpact();
                }
                widget.onLongPress!();
              }
              : null,
      child: containerWidget,
    );

    // Add mouse region for hover detection
    Widget hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: gestureWidget,
    );

    // Add focus handling
    Widget focusWidget = Focus(focusNode: _focusNode, child: hoverWidget);

    // Add tooltip if needed
    if (widget.tooltip != null) {
      focusWidget = Tooltip(message: widget.tooltip!, child: focusWidget);
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      focusWidget = Semantics(
        label: widget.semanticsLabel,
        value: timeText,
        child: focusWidget,
      );
    }

    return focusWidget;
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveInputFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large (>1920px) - Reduced for better fit
  } else if (screenWidth >= 1440) {
    return 15.0; // Large (1440-1920px) - Reduced for better fit
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium (1280-1366px) - Standard size
  } else if (screenWidth >= 768) {
    return 14.0; // Small (768-1024px) - Increased for readability
  } else {
    return 14.0; // Default for very small screens - Consistent
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 22.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 18.0; // Large
  } else if (screenWidth >= 1280) {
    return 18.0; // Medium
  } else if (screenWidth >= 768) {
    return 16.0; // Small
  } else {
    return 14.0; // Extra Small (fallback for very small screens)
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 12.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
